-- PostgreSQL Database Setup for Dental AI System
-- Run this script in your PostgreSQL database (pgAdmin or psql)

-- Create database (run this as superuser)
-- CREATE DATABASE dental_ai;

-- Connect to dental_ai database and run the following:

-- 1. Users table for authentication and user management
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'dentist')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 2. Uploads table for tracking document uploads
CREATE TABLE IF NOT EXISTS uploads (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'docx', 'pptx', 'jpg', 'png', 'dicom')),
    file_size INTEGER,
    file_path TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    processing_info JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Chat history for conversation tracking
CREATE TABLE IF NOT EXISTS chat_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    upload_id INTEGER REFERENCES uploads(id) ON DELETE SET NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    context_type TEXT DEFAULT 'general' CHECK (context_type IN ('general', 'document', 'knowledge_graph')),
    difficulty_level TEXT DEFAULT 'normal' CHECK (difficulty_level IN ('simple', 'normal', 'detailed')),
    response_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. Knowledge graph entities (for PostgreSQL backup/search)
CREATE TABLE IF NOT EXISTS kg_entities (
    id SERIAL PRIMARY KEY,
    upload_id INTEGER REFERENCES uploads(id) ON DELETE CASCADE,
    entity_text TEXT NOT NULL,
    entity_type TEXT NOT NULL CHECK (entity_type IN ('DISEASE', 'SYMPTOM', 'PROCEDURE', 'MATERIAL', 'INSTRUMENT', 'ANATOMICAL_STRUCTURE')),
    confidence REAL DEFAULT 1.0,
    start_pos INTEGER,
    end_pos INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. Knowledge graph relations (for PostgreSQL backup/search)
CREATE TABLE IF NOT EXISTS kg_relations (
    id SERIAL PRIMARY KEY,
    upload_id INTEGER REFERENCES uploads(id) ON DELETE CASCADE,
    head_entity TEXT NOT NULL,
    tail_entity TEXT NOT NULL,
    relation_type TEXT NOT NULL CHECK (relation_type IN ('causes', 'treats', 'indicates', 'uses_material', 'part_of', 'has_symptom')),
    confidence REAL DEFAULT 1.0,
    source_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. Document chunks for RAG (vector embeddings)
CREATE TABLE IF NOT EXISTS document_chunks (
    id SERIAL PRIMARY KEY,
    upload_id INTEGER REFERENCES uploads(id) ON DELETE CASCADE,
    chunk_text TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    embedding VECTOR(384), -- For sentence-transformers all-MiniLM-L6-v2
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_uploads_user_id ON uploads(user_id);
CREATE INDEX IF NOT EXISTS idx_uploads_status ON uploads(status);
CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_upload_id ON chat_history(upload_id);
CREATE INDEX IF NOT EXISTS idx_kg_entities_upload_id ON kg_entities(upload_id);
CREATE INDEX IF NOT EXISTS idx_kg_entities_type ON kg_entities(entity_type);
CREATE INDEX IF NOT EXISTS idx_kg_relations_upload_id ON kg_relations(upload_id);
CREATE INDEX IF NOT EXISTS idx_kg_relations_type ON kg_relations(relation_type);
CREATE INDEX IF NOT EXISTS idx_document_chunks_upload_id ON document_chunks(upload_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_uploads_updated_at BEFORE UPDATE ON uploads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample admin user (password: 'admin123' - change this!)
INSERT INTO users (name, email, password_hash, role) 
VALUES ('Admin User', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtO5S5EM.S', 'admin')
ON CONFLICT (email) DO NOTHING;

-- Sample data for testing
INSERT INTO users (name, email, password_hash, role) 
VALUES 
    ('Dr. Smith', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtO5S5EM.S', 'dentist'),
    ('Student User', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtO5S5EM.S', 'user')
ON CONFLICT (email) DO NOTHING;

-- Display created tables
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name IN ('users', 'uploads', 'chat_history', 'kg_entities', 'kg_relations', 'document_chunks')
ORDER BY table_name, ordinal_position;
