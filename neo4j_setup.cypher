// Neo4j Setup for Dental AI Knowledge Graph
// Run these commands in Neo4j Browser (http://localhost:7474)

// 1. Clear existing data (optional - use with caution!)
// MATCH (n) DETACH DELETE n;

// 2. Create constraints for better performance and data integrity
CREATE CONSTRAINT entity_name_unique IF NOT EXISTS FOR (e:Entity) REQUIRE e.name IS UNIQUE;
CREATE CONSTRAINT entity_name_not_null IF NOT EXISTS FOR (e:Entity) REQUIRE e.name IS NOT NULL;

// 3. Create indexes for better query performance
CREATE INDEX entity_type_index IF NOT EXISTS FOR (e:Entity) ON (e.type);
CREATE INDEX entity_upload_index IF NOT EXISTS FOR (e:Entity) ON (e.upload_id);
CREATE INDEX relation_type_index IF NOT EXISTS FOR ()-[r:RELATES]-() ON (r.type);

// 4. Sample dental knowledge graph data for testing
// Create dental entities
MERGE (gingivitis:Entity {name: "Gingivitis", type: "DISEASE", confidence: 1.0})
MERGE (bleeding_gums:Entity {name: "Bleeding Gums", type: "SYMPTOM", confidence: 1.0})
MERGE (dental_cleaning:Entity {name: "Dental Cleaning", type: "PROCEDURE", confidence: 1.0})
MERGE (ultrasonic_scaler:Entity {name: "Ultrasonic Scaler", type: "INSTRUMENT", confidence: 1.0})
MERGE (periodontitis:Entity {name: "Periodontitis", type: "DISEASE", confidence: 1.0})
MERGE (loose_teeth:Entity {name: "Loose Teeth", type: "SYMPTOM", confidence: 1.0})
MERGE (gum_recession:Entity {name: "Gum Recession", type: "SYMPTOM", confidence: 1.0})
MERGE (dental_caries:Entity {name: "Dental Caries", type: "DISEASE", confidence: 1.0})
MERGE (toothache:Entity {name: "Toothache", type: "SYMPTOM", confidence: 1.0})
MERGE (dental_filling:Entity {name: "Dental Filling", type: "PROCEDURE", confidence: 1.0})
MERGE (composite_resin:Entity {name: "Composite Resin", type: "MATERIAL", confidence: 1.0})
MERGE (amalgam:Entity {name: "Amalgam", type: "MATERIAL", confidence: 1.0})
MERGE (root_canal:Entity {name: "Root Canal Treatment", type: "PROCEDURE", confidence: 1.0})
MERGE (infected_pulp:Entity {name: "Infected Pulp", type: "DISEASE", confidence: 1.0})
MERGE (gutta_percha:Entity {name: "Gutta-percha", type: "MATERIAL", confidence: 1.0})
MERGE (enamel:Entity {name: "Enamel", type: "ANATOMICAL_STRUCTURE", confidence: 1.0})
MERGE (dentin:Entity {name: "Dentin", type: "ANATOMICAL_STRUCTURE", confidence: 1.0})
MERGE (pulp:Entity {name: "Pulp", type: "ANATOMICAL_STRUCTURE", confidence: 1.0})
MERGE (tooth_crown:Entity {name: "Tooth Crown", type: "ANATOMICAL_STRUCTURE", confidence: 1.0})
MERGE (dental_probe:Entity {name: "Dental Probe", type: "INSTRUMENT", confidence: 1.0})
MERGE (forceps:Entity {name: "Forceps", type: "INSTRUMENT", confidence: 1.0})
MERGE (tooth_extraction:Entity {name: "Tooth Extraction", type: "PROCEDURE", confidence: 1.0});

// Create relationships between entities
MATCH (gingivitis:Entity {name: "Gingivitis"}), (bleeding_gums:Entity {name: "Bleeding Gums"})
MERGE (gingivitis)-[r1:RELATES {type: "causes", confidence: 0.9}]->(bleeding_gums);

MATCH (gingivitis:Entity {name: "Gingivitis"}), (periodontitis:Entity {name: "Periodontitis"})
MERGE (gingivitis)-[r2:RELATES {type: "causes", confidence: 0.8}]->(periodontitis);

MATCH (gingivitis:Entity {name: "Gingivitis"}), (dental_cleaning:Entity {name: "Dental Cleaning"})
MERGE (dental_cleaning)-[r3:RELATES {type: "treats", confidence: 0.9}]->(gingivitis);

MATCH (dental_cleaning:Entity {name: "Dental Cleaning"}), (ultrasonic_scaler:Entity {name: "Ultrasonic Scaler"})
MERGE (dental_cleaning)-[r4:RELATES {type: "uses_material", confidence: 0.8}]->(ultrasonic_scaler);

MATCH (periodontitis:Entity {name: "Periodontitis"}), (loose_teeth:Entity {name: "Loose Teeth"})
MERGE (periodontitis)-[r5:RELATES {type: "has_symptom", confidence: 0.8}]->(loose_teeth);

MATCH (periodontitis:Entity {name: "Periodontitis"}), (gum_recession:Entity {name: "Gum Recession"})
MERGE (periodontitis)-[r6:RELATES {type: "has_symptom", confidence: 0.8}]->(gum_recession);

MATCH (dental_caries:Entity {name: "Dental Caries"}), (toothache:Entity {name: "Toothache"})
MERGE (dental_caries)-[r7:RELATES {type: "has_symptom", confidence: 0.9}]->(toothache);

MATCH (toothache:Entity {name: "Toothache"}), (dental_caries:Entity {name: "Dental Caries"})
MERGE (toothache)-[r8:RELATES {type: "indicates", confidence: 0.8}]->(dental_caries);

MATCH (dental_caries:Entity {name: "Dental Caries"}), (dental_filling:Entity {name: "Dental Filling"})
MERGE (dental_filling)-[r9:RELATES {type: "treats", confidence: 0.9}]->(dental_caries);

MATCH (dental_filling:Entity {name: "Dental Filling"}), (composite_resin:Entity {name: "Composite Resin"})
MERGE (dental_filling)-[r10:RELATES {type: "uses_material", confidence: 0.8}]->(composite_resin);

MATCH (dental_filling:Entity {name: "Dental Filling"}), (amalgam:Entity {name: "Amalgam"})
MERGE (dental_filling)-[r11:RELATES {type: "uses_material", confidence: 0.8}]->(amalgam);

MATCH (infected_pulp:Entity {name: "Infected Pulp"}), (root_canal:Entity {name: "Root Canal Treatment"})
MERGE (root_canal)-[r12:RELATES {type: "treats", confidence: 0.9}]->(infected_pulp);

MATCH (root_canal:Entity {name: "Root Canal Treatment"}), (gutta_percha:Entity {name: "Gutta-percha"})
MERGE (root_canal)-[r13:RELATES {type: "uses_material", confidence: 0.9}]->(gutta_percha);

MATCH (enamel:Entity {name: "Enamel"}), (tooth_crown:Entity {name: "Tooth Crown"})
MERGE (enamel)-[r14:RELATES {type: "part_of", confidence: 1.0}]->(tooth_crown);

MATCH (dentin:Entity {name: "Dentin"}), (tooth_crown:Entity {name: "Tooth Crown"})
MERGE (dentin)-[r15:RELATES {type: "part_of", confidence: 1.0}]->(tooth_crown);

MATCH (pulp:Entity {name: "Pulp"}), (tooth_crown:Entity {name: "Tooth Crown"})
MERGE (pulp)-[r16:RELATES {type: "part_of", confidence: 1.0}]->(tooth_crown);

MATCH (tooth_extraction:Entity {name: "Tooth Extraction"}), (forceps:Entity {name: "Forceps"})
MERGE (tooth_extraction)-[r17:RELATES {type: "uses_material", confidence: 0.8}]->(forceps);

// 5. Useful queries for testing and exploration

// Query 1: Find all diseases and their symptoms
// MATCH (d:Entity {type: "DISEASE"})-[r:RELATES {type: "has_symptom"}]->(s:Entity {type: "SYMPTOM"})
// RETURN d.name as Disease, s.name as Symptom;

// Query 2: Find all procedures and what they treat
// MATCH (p:Entity {type: "PROCEDURE"})-[r:RELATES {type: "treats"}]->(d:Entity {type: "DISEASE"})
// RETURN p.name as Procedure, d.name as Treats;

// Query 3: Find all materials used in procedures
// MATCH (p:Entity {type: "PROCEDURE"})-[r:RELATES {type: "uses_material"}]->(m:Entity {type: "MATERIAL"})
// RETURN p.name as Procedure, m.name as Material;

// Query 4: Find anatomical structures and their relationships
// MATCH (a:Entity {type: "ANATOMICAL_STRUCTURE"})-[r:RELATES]->(b:Entity)
// RETURN a.name as Structure, r.type as Relationship, b.name as Related, b.type as Type;

// Query 5: Find shortest path between two entities (example)
// MATCH path = shortestPath((a:Entity {name: "Gingivitis"})-[*]-(b:Entity {name: "Ultrasonic Scaler"}))
// RETURN path;

// Query 6: Get graph statistics
// MATCH (e:Entity)
// OPTIONAL MATCH (e)-[r:RELATES]-()
// RETURN 
//     COUNT(DISTINCT e) as total_entities,
//     COUNT(DISTINCT r) as total_relations,
//     COUNT(DISTINCT e.type) as entity_types,
//     COUNT(DISTINCT r.type) as relation_types;

// Query 7: Find entities by type
// MATCH (e:Entity {type: "DISEASE"}) RETURN e.name as Disease ORDER BY e.name;

// Query 8: Find all relationships of a specific type
// MATCH (a:Entity)-[r:RELATES {type: "causes"}]->(b:Entity)
// RETURN a.name as Cause, b.name as Effect;

// 6. Create sample upload data for testing
MATCH (e:Entity)
SET e.upload_id = 1, e.created_at = datetime();

// Display final statistics
MATCH (e:Entity)
OPTIONAL MATCH (e)-[r:RELATES]-()
RETURN 
    COUNT(DISTINCT e) as total_entities,
    COUNT(DISTINCT r) as total_relations,
    COUNT(DISTINCT e.type) as entity_types,
    COUNT(DISTINCT r.type) as relation_types;
