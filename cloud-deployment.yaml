# Google Cloud Run Deployment Configuration
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: addss-dental-ai
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/cpu: "2"
        run.googleapis.com/max-scale: "10"
        run.googleapis.com/min-scale: "1"
    spec:
      containerConcurrency: 10
      timeoutSeconds: 300
      containers:
      - image: gcr.io/PROJECT_ID/addss-dental-ai:latest
        ports:
        - containerPort: 8080
        env:
        - name: POSTGRES_HOST
          value: "CLOUD_SQL_CONNECTION_NAME"
        - name: POSTGRES_DB
          value: "dental_ai"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: password
        - name: NEO4J_URI
          value: "bolt://NEO4J_INSTANCE_IP:7687"
        - name: NEO4J_USER
          valueFrom:
            secretKeyRef:
              name: neo4j-credentials
              key: username
        - name: NEO4J_PASSWORD
          valueFrom:
            secretKeyRef:
              name: neo4j-credentials
              key: password
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: gemini-key
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: openai-key
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
          requests:
            cpu: "1"
            memory: "2Gi"
        volumeMounts:
        - name: cloudsql
          mountPath: /cloudsql
          readOnly: true
      volumes:
      - name: cloudsql
        csi:
          driver: gcp-compute-persistent-disk-csi-driver
          readOnly: true
          volumeAttributes:
            secretName: cloudsql-instance-credentials

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: addss-dental-ai-hpa
spec:
  scaleTargetRef:
    apiVersion: serving.knative.dev/v1
    kind: Service
    name: addss-dental-ai
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
