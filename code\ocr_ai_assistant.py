import os
import warnings
import logging
import sys

# Suppress warnings and errors
warnings.filterwarnings("ignore")
logging.getLogger("torch").setLevel(logging.ERROR)
logging.getLogger("transformers").setLevel(logging.ERROR)
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

import streamlit as st
import fitz  # PyMuPDF
import requests
import faiss
import numpy as np
import google.generativeai as genai
from googlesearch import search
import openai
from bs4 import BeautifulSoup
import time
import re
from dotenv import load_dotenv
from PIL import Image
import io

# Import libraries with error handling
try:
    from sentence_transformers import SentenceTransformer
    import pytesseract
    import cv2
except Exception as e:
    st.error(f"Error importing required libraries: {e}")
    st.error("Please install: pip install pytesseract opencv-python pillow")
    st.stop()

# Load API keys (works both locally and on Streamlit Cloud)
load_dotenv(dotenv_path='key.env')
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "") or st.secrets.get("GEMINI_API_KEY", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "") or st.secrets.get("OPENAI_API_KEY", "")

# Configure APIs
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
if OPENAI_API_KEY:
    openai.api_key = OPENAI_API_KEY

UPLOAD_FOLDER = "uploaded_pdfs"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

class OCRPDFAssistant:
    def __init__(self):
        self.documents_content = ""
        self.has_documents = False
        self.document_chunks = []
        self.embeddings = None
        self.index = None
        
        # Initialize model with error handling
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                self.model = SentenceTransformer('all-MiniLM-L6-v2')
        except Exception as e:
            st.error(f"Error loading embedding model: {e}")
            self.model = None
        
        # Check if Tesseract is available
        self.ocr_available = self._check_tesseract()
        
    def _check_tesseract(self):
        """Check if Tesseract OCR is available"""
        try:
            pytesseract.get_tesseract_version()
            return True
        except:
            st.warning("⚠️ Tesseract OCR not found. Scanned PDF processing will be limited.")
            st.info("💡 To enable OCR: Install Tesseract from https://github.com/tesseract-ocr/tesseract")
            return False
    
    def add_documents(self, pdf_files):
        """Process uploaded documents with OCR support"""
        all_text = ""
        self.document_chunks = []
        
        for pdf_file in pdf_files:
            save_path = os.path.join(UPLOAD_FOLDER, pdf_file.name)
            with open(save_path, "wb") as f:
                f.write(pdf_file.getbuffer())
        
        for filename in os.listdir(UPLOAD_FOLDER):
            if filename.endswith('.pdf'):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                
                # Try regular text extraction first
                text = self._extract_pdf_text(file_path)
                
                # If no text found and OCR available, try OCR
                if (not text or len(text.strip()) < 100) and self.ocr_available:
                    st.info(f"🔍 No text found in {filename}, trying OCR...")
                    ocr_text = self._extract_pdf_with_ocr(file_path)
                    if ocr_text:
                        text = ocr_text
                        st.success(f"✅ OCR successful for {filename}")
                
                if text:
                    all_text += f"\n\n=== From {filename} ===\n{text}"
                    chunks = self._create_chunks(text, filename)
                    self.document_chunks.extend(chunks)
                else:
                    st.warning(f"⚠️ Could not extract text from {filename}")
        
        if all_text:
            self.documents_content = all_text
            self.has_documents = True
            
            if self.document_chunks and self.model:
                try:
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        chunk_texts = [chunk['text'] for chunk in self.document_chunks]
                        self.embeddings = self.model.encode(chunk_texts)
                        dimension = self.embeddings.shape[1]
                        self.index = faiss.IndexFlatIP(dimension)
                        faiss.normalize_L2(self.embeddings)
                        self.index.add(self.embeddings)
                except Exception as e:
                    st.warning(f"Warning: Could not create embeddings: {e}")
                    self.index = None
            
            return True
        return False
    
    def _extract_pdf_text(self, pdf_path):
        """Extract text using regular PyMuPDF"""
        try:
            doc = fitz.open(pdf_path)
            text = ""
            for page in doc:
                text += page.get_text() + "\n"
            doc.close()
            return text
        except:
            return ""
    
    def _extract_pdf_with_ocr(self, pdf_path):
        """Extract text using OCR for scanned PDFs"""
        if not self.ocr_available:
            return ""
        
        try:
            doc = fitz.open(pdf_path)
            all_text = ""
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Convert page to image
                mat = fitz.Matrix(2.0, 2.0)  # Higher resolution for better OCR
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Convert to PIL Image
                image = Image.open(io.BytesIO(img_data))
                
                # Convert to numpy array for OpenCV
                img_array = np.array(image)
                
                # Preprocess image for better OCR
                if len(img_array.shape) == 3:
                    gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                else:
                    gray = img_array
                
                # Apply image preprocessing
                # Increase contrast
                gray = cv2.convertScaleAbs(gray, alpha=1.2, beta=10)
                
                # Denoise
                gray = cv2.medianBlur(gray, 3)
                
                # Perform OCR
                custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?;:()[]{}"\'-+=/\\@#$%^&*<>|`~_ '
                text = pytesseract.image_to_string(gray, config=custom_config)
                
                if text.strip():
                    all_text += f"\n--- Page {page_num + 1} ---\n{text}\n"
            
            doc.close()
            return all_text
            
        except Exception as e:
            st.error(f"OCR Error: {str(e)}")
            return ""
    
    def _create_chunks(self, text, filename):
        chunks = []
        words = text.split()
        chunk_size = 300
        overlap = 50
        
        for i in range(0, len(words), chunk_size - overlap):
            chunk_words = words[i:i + chunk_size]
            chunk_text = ' '.join(chunk_words)
            
            if len(chunk_text.strip()) > 100:
                chunks.append({
                    'text': chunk_text,
                    'source': filename,
                    'chunk_id': len(chunks)
                })
        
        return chunks
    
    def search_documents(self, question):
        """Search documents first"""
        if not self.has_documents or not self.index or not self.model:
            return None, "No documents uploaded or search not available"
        
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                query_embedding = self.model.encode([question])
                faiss.normalize_L2(query_embedding)
                
                scores, indices = self.index.search(query_embedding, min(3, len(self.document_chunks)))
                
                relevant_chunks = []
                for score, idx in zip(scores[0], indices[0]):
                    if score > 0.3:
                        chunk = self.document_chunks[idx]
                        relevant_chunks.append({
                            'text': chunk['text'],
                            'source': chunk['source'],
                            'score': float(score)
                        })
                
                if relevant_chunks:
                    combined_text = "\n\n".join([f"From {chunk['source']}:\n{chunk['text']}" for chunk in relevant_chunks])
                    return combined_text, f"Found {len(relevant_chunks)} relevant sections"
                else:
                    return None, "No relevant information found in PDFs"
                    
        except Exception as e:
            return None, f"Error searching documents: {str(e)}"

    def answer_question(self, question):
        """Answer questions with PDF search first"""
        
        # Step 1: Search PDFs first
        pdf_content, pdf_status = self.search_documents(question)
        
        # Step 2: Generate response
        if pdf_content:
            return self._generate_response(question, pdf_content, "PDF")
        else:
            return self._generate_response(question, "", "AI", pdf_status)
    
    def _generate_response(self, question, context, source_type="AI", pdf_status=""):
        """Generate intelligent responses"""
        
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            if source_type == "PDF":
                prompt = f"""You are an expert AI assistant. Answer the question using the information from the user's documents.

CONTENT FROM USER'S DOCUMENTS:
{context}

Question: {question}

Provide a clear, comprehensive answer based on the document content. Explain concepts thoroughly and add your insights to make it educational."""

                response = model.generate_content(prompt)
                return {
                    'answer': response.text,
                    'source_type': source_type,
                    'pdf_status': pdf_status,
                    'method': 'Your PDF Documents (OCR Enhanced)'
                }
            else:
                prompt = f"""You are an expert AI assistant. Answer this question comprehensively with clear explanations, examples, and practical insights.

Question: {question}

Provide a complete, expert-level answer that fully explains the concept."""

                response = model.generate_content(prompt)
                return {
                    'answer': response.text,
                    'source_type': source_type,
                    'pdf_status': pdf_status,
                    'method': 'Gemini AI'
                }
            
        except:
            # Fallback responses
            fallback_answers = {
                "machine learning": """Machine learning is a branch of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed for every task.

**Core Concept:**
Instead of writing specific instructions for every scenario, we show computers examples and let them figure out patterns and make decisions on their own.

**Three Main Types:**

1. **Supervised Learning** - Learning with examples and correct answers
   - Like studying with answer keys
   - Examples: Email spam detection, medical diagnosis, price prediction

2. **Unsupervised Learning** - Finding patterns without guidance  
   - Like exploring data to discover hidden insights
   - Examples: Customer segmentation, anomaly detection

3. **Reinforcement Learning** - Learning through trial and error
   - Like learning to play a game through practice
   - Examples: Game playing AI, robotics, autonomous vehicles

**Real-World Applications:**
- Netflix recommendations
- Voice assistants (Siri, Alexa)
- Photo tagging on social media
- GPS route optimization
- Fraud detection in banking

**How It Works:**
1. Collect relevant data
2. Choose appropriate algorithm
3. Train the model on data
4. Test performance
5. Deploy for predictions

Machine learning is transforming every industry by automating decision-making and finding patterns humans might miss!""",

                "hyperplane": """A hyperplane is a fundamental concept in mathematics and machine learning that's easier to understand than it sounds!

**Simple Definition:**
A hyperplane is a flat surface that divides space into two parts. Think of it as:
- In 2D: A line divides a plane into two halves
- In 3D: A plane divides space into two regions  
- In higher dimensions: A hyperplane does the same thing

**Visual Analogy:**
Imagine stretching a large sheet across a room from wall to wall. That sheet divides the room into two spaces (above and below). That's essentially what a hyperplane does in higher dimensions.

**Why Hyperplanes Matter:**

1. **Machine Learning Classification:**
   - Support Vector Machines use hyperplanes as decision boundaries
   - They separate different classes of data
   - Example: Separating emails into "spam" vs "not spam"

2. **Mathematical Properties:**
   - Always one dimension less than the space it's in
   - Defined by equation: w₁x₁ + w₂x₂ + ... + wₙxₙ + b = 0
   - Perfectly flat (no curves)

**Real-World Examples:**
- Credit scoring: Separate "approve" from "deny" based on income, credit score, etc.
- Medical diagnosis: Distinguish "healthy" from "disease" based on test results
- Image recognition: Classify different objects in photos

The beauty of hyperplanes is they provide a clean, mathematical way to make decisions in complex, multi-dimensional problems!"""
            }
            
            question_lower = question.lower()
            for key, answer in fallback_answers.items():
                if key in question_lower:
                    return {
                        'answer': answer,
                        'source_type': source_type,
                        'pdf_status': pdf_status,
                        'method': 'Knowledge Base'
                    }
            
            return {
                'answer': f"I'd be happy to help explain {question}! Could you be more specific about what aspect you'd like to know? I can provide detailed explanations on any topic.",
                'source_type': source_type,
                'pdf_status': pdf_status,
                'method': 'Interactive Assistant'
            }

def main():
    # Suppress Streamlit warnings
    st.set_page_config(
        page_title="🔍 OCR PDF AI Assistant",
        layout="wide",
        initial_sidebar_state="collapsed"
    )

    # Hide Streamlit style
    hide_streamlit_style = """
    <style>
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {display:none;}
    </style>
    """
    st.markdown(hide_streamlit_style, unsafe_allow_html=True)

    st.title("🔍 OCR PDF AI Assistant")
    st.markdown("**Upload PDFs (including scanned documents) and ask questions - I'll search your documents first with OCR support!**")

    # OCR Status
    if 'assistant' not in st.session_state:
        st.session_state.assistant = OCRPDFAssistant()

    # Show OCR capability status
    if st.session_state.assistant.ocr_available:
        st.success("✅ **OCR Enabled** - Can process scanned PDFs and handwritten notes!")
    else:
        st.warning("⚠️ **OCR Not Available** - Only text-based PDFs will work")
        with st.expander("🔧 How to enable OCR"):
            st.markdown("""
            **To process scanned PDFs, install Tesseract OCR:**

            **Windows:**
            1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
            2. Install and add to PATH
            3. Restart this application

            **Python packages needed:**
            ```bash
            pip install pytesseract opencv-python pillow
            ```
            """)

    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []

    # Document upload
    st.header("📚 Upload Your Documents")
    st.info("💡 **Supports both regular PDFs and scanned documents (handwritten notes, photos of text, etc.)**")
    uploaded_files = st.file_uploader("Choose PDF files", type="pdf", accept_multiple_files=True)

    if uploaded_files:
        with st.spinner("Processing PDFs (including OCR for scanned documents)..."):
            success = st.session_state.assistant.add_documents(uploaded_files)
            if success:
                st.success(f"✅ Successfully processed {len(uploaded_files)} PDFs")
            else:
                st.error("❌ Could not process PDFs")

    # Main interface
    st.header("💬 Ask Your Question")

    # Example questions
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("🤖 What is machine learning?"):
            st.session_state.current_q = "What is machine learning?"
    with col2:
        if st.button("🔍 What is a hyperplane?"):
            st.session_state.current_q = "What is a hyperplane?"
    with col3:
        if st.button("📊 Explain neural networks"):
            st.session_state.current_q = "How do neural networks work?"

    # Question input
    question = st.text_input(
        "Your question:",
        value=st.session_state.get('current_q', ''),
        placeholder="Ask anything - I'll search your PDFs (including scanned ones) first!"
    )

    if 'current_q' in st.session_state:
        del st.session_state.current_q

    if st.button("🚀 Get Answer", type="primary") and question:
        with st.spinner("🔍 Searching PDFs (with OCR) and generating answer..."):

            response = st.session_state.assistant.answer_question(question)

            # Show PDF search status with complete transparency
            if st.session_state.assistant.has_documents:
                if response.get('source_type') == 'PDF':
                    st.success("✅ **ANSWER SOURCE: Your PDF Documents**")
                    st.info("📄 The answer below is based on content found in your uploaded PDFs (including OCR-processed content)")

                    # Show which PDF sections were used
                    pdf_content, _ = st.session_state.assistant.search_documents(question)
                    if pdf_content:
                        with st.expander("📋 View PDF content used for this answer"):
                            st.text(pdf_content[:1000] + "..." if len(pdf_content) > 1000 else pdf_content)
                else:
                    st.warning(f"⚠️ **PDF Search Result:** {response.get('pdf_status', 'No relevant content found')}")
                    st.error("❌ **ANSWER SOURCE: AI Knowledge (NOT from your PDFs)**")
                    st.info("💡 The answer below is from AI general knowledge, not your documents")
            else:
                st.info("📝 **No PDFs uploaded**")
                st.error("❌ **ANSWER SOURCE: AI Knowledge (No PDFs to search)**")

            # Display answer with clear labeling
            if response.get('source_type') == 'PDF':
                st.subheader("🎯 Answer (From Your PDFs)")
            else:
                st.subheader("🎯 Answer (From AI Knowledge)")

            st.write(response['answer'])

            # Show detailed source information
            source_type = response.get('source_type', 'AI')
            method = response.get('method', 'Unknown')

            if source_type == 'PDF':
                st.success(f"📚 **Confirmed Source:** Your PDF Documents | **Processing:** {method}")
            else:
                st.info(f"🤖 **Confirmed Source:** AI General Knowledge | **Method:** {method}")

            # Add to history
            st.session_state.chat_history.append({
                'question': question,
                'answer': response['answer'],
                'source_type': source_type,
                'method': method
            })

    # Chat history
    if st.session_state.chat_history:
        st.header("💬 Recent Questions")
        for i, chat in enumerate(st.session_state.chat_history[-3:]):
            with st.expander(f"Q: {chat['question'][:50]}..."):
                st.write(f"**Question:** {chat['question']}")
                st.write(f"**Answer:** {chat['answer'][:300]}...")

                source_type = chat.get('source_type', 'AI')
                method = chat.get('method', 'Unknown')

                if source_type == 'PDF':
                    st.caption(f"📚 Source: Your PDFs | Method: {method}")
                else:
                    st.caption(f"🤖 Source: AI Knowledge | Method: {method}")

        if st.button("🗑️ Clear History"):
            st.session_state.chat_history = []
            st.rerun()

if __name__ == "__main__":
    main()
