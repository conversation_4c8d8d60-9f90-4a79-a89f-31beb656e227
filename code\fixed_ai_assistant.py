import os
import warnings
import logging
import sys

# Suppress warnings and errors
warnings.filterwarnings("ignore")
logging.getLogger("torch").setLevel(logging.ERROR)
logging.getLogger("transformers").setLevel(logging.ERROR)
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

import streamlit as st
import fitz  # PyMuPDF
import requests
import faiss
import numpy as np
import google.generativeai as genai
from googlesearch import search
import openai
from bs4 import BeautifulSoup
import time
import re
from dotenv import load_dotenv

# Import SentenceTransformer with better error handling
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMER_AVAILABLE = True
except Exception as e:
    print(f"Warning: SentenceTransformer not available: {e}")
    SENTENCE_TRANSFORMER_AVAILABLE = False

# Load API keys (works both locally and on Streamlit Cloud)
load_dotenv(dotenv_path='key.env')
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "") or st.secrets.get("GEMINI_API_KEY", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "") or st.secrets.get("OPENAI_API_KEY", "")

# Configure APIs
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
if OPENAI_API_KEY:
    openai.api_key = OPENAI_API_KEY

UPLOAD_FOLDER = "uploaded_pdfs"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

class PDFAssistant:
    def __init__(self):
        self.documents_content = ""
        self.has_documents = False
        self.document_chunks = []
        self.embeddings = None
        self.index = None

        # Initialize model with better error handling
        if SENTENCE_TRANSFORMER_AVAILABLE:
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    self.model = SentenceTransformer('all-MiniLM-L6-v2')
            except Exception as e:
                print(f"Warning: Could not load embedding model: {e}")
                self.model = None
        else:
            self.model = None
        
    def add_documents(self, pdf_files):
        """Process uploaded documents"""
        all_text = ""
        self.document_chunks = []
        
        for pdf_file in pdf_files:
            save_path = os.path.join(UPLOAD_FOLDER, pdf_file.name)
            with open(save_path, "wb") as f:
                f.write(pdf_file.getbuffer())
        
        for filename in os.listdir(UPLOAD_FOLDER):
            if filename.endswith('.pdf'):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                text = self._extract_pdf_text(file_path)
                if text:
                    all_text += f"\n\n=== From {filename} ===\n{text}"
                    chunks = self._create_chunks(text, filename)
                    self.document_chunks.extend(chunks)
        
        if all_text:
            self.documents_content = all_text
            self.has_documents = True
            
            if self.document_chunks and self.model:
                try:
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        chunk_texts = [chunk['text'] for chunk in self.document_chunks]
                        self.embeddings = self.model.encode(chunk_texts)
                        dimension = self.embeddings.shape[1]
                        self.index = faiss.IndexFlatIP(dimension)
                        faiss.normalize_L2(self.embeddings)
                        self.index.add(self.embeddings)
                except Exception as e:
                    st.warning(f"Warning: Could not create embeddings: {e}")
                    self.index = None
            
            return True
        return False
    
    def _extract_pdf_text(self, pdf_path):
        try:
            doc = fitz.open(pdf_path)
            text = ""
            for page in doc:
                text += page.get_text() + "\n"
            doc.close()
            return text
        except:
            return ""
    
    def _create_chunks(self, text, filename):
        chunks = []
        words = text.split()
        chunk_size = 300
        overlap = 50
        
        for i in range(0, len(words), chunk_size - overlap):
            chunk_words = words[i:i + chunk_size]
            chunk_text = ' '.join(chunk_words)
            
            if len(chunk_text.strip()) > 100:
                chunks.append({
                    'text': chunk_text,
                    'source': filename,
                    'chunk_id': len(chunks)
                })
        
        return chunks
    
    def search_documents(self, question):
        """Search documents first"""
        if not self.has_documents or not self.index or not self.model:
            return None, "No documents uploaded or search not available"

        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                query_embedding = self.model.encode([question])
                faiss.normalize_L2(query_embedding)

                scores, indices = self.index.search(query_embedding, min(3, len(self.document_chunks)))

                relevant_chunks = []
                for score, idx in zip(scores[0], indices[0]):
                    if score > 0.3:
                        chunk = self.document_chunks[idx]
                        relevant_chunks.append({
                            'text': chunk['text'],
                            'source': chunk['source'],
                            'score': float(score)
                        })

                if relevant_chunks:
                    combined_text = "\n\n".join([f"From {chunk['source']}:\n{chunk['text']}" for chunk in relevant_chunks])
                    return combined_text, f"Found {len(relevant_chunks)} relevant sections"
                else:
                    return None, "No relevant information found in PDFs"

        except Exception as e:
            return None, f"Error searching documents: {str(e)}"

    def answer_question(self, question, conversation_history=None, difficulty_level="normal"):
        """Answer questions with PDF search first and conversation context"""

        # Check if this is a summarization request
        if self._is_summarization_request(question):
            return self._handle_summarization(question, conversation_history, difficulty_level)

        # Step 1: Search PDFs first
        pdf_content, pdf_status = self.search_documents(question)

        # Step 2: Generate response with conversation context
        if pdf_content:
            return self._generate_response(question, pdf_content, "PDF", "", conversation_history, difficulty_level)
        else:
            return self._generate_response(question, "", "AI", pdf_status, conversation_history, difficulty_level)

    def _is_summarization_request(self, question):
        """Check if the question is asking for summarization"""
        summarization_keywords = [
            "summarize", "summary", "summarise", "overview", "outline",
            "key points", "main points", "brief", "gist", "recap",
            "entire pdf", "whole document", "complete unit", "full chapter",
            "all topics", "everything about", "comprehensive overview"
        ]
        question_lower = question.lower()
        return any(keyword in question_lower for keyword in summarization_keywords)

    def _handle_summarization(self, question, conversation_history=None, difficulty_level="normal"):
        """Handle summarization requests for PDFs or topics"""

        if not self.has_documents:
            return {
                'answer': "I don't have any documents uploaded to summarize. Please upload your PDFs first, then I can provide summaries of the content.",
                'source_type': 'AI',
                'pdf_status': 'No documents available',
                'method': 'Summarization Assistant',
                'difficulty': difficulty_level,
                'is_summary': True
            }

        # Use all document content for comprehensive summarization
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')

            # Build conversation context
            context_prompt = ""
            if conversation_history:
                recent_context = conversation_history[-2:]  # Last 2 exchanges for context
                context_prompt = "\n\nPREVIOUS CONVERSATION CONTEXT:\n"
                for i, chat in enumerate(recent_context):
                    context_prompt += f"Q{i+1}: {chat['question']}\nA{i+1}: {chat['answer'][:150]}...\n"

            # Adjust difficulty level for summary
            difficulty_instructions = {
                "simple": "Create a simple, easy-to-understand summary with clear explanations and examples. Use bullet points and avoid technical jargon.",
                "normal": "Provide a comprehensive summary with key concepts, main points, and important details organized clearly.",
                "detailed": "Give a thorough, detailed summary covering all major concepts, subtopics, technical details, and relationships between ideas."
            }

            difficulty_instruction = difficulty_instructions.get(difficulty_level, difficulty_instructions["normal"])

            prompt = f"""You are an expert academic tutor creating summaries for exam preparation.

{context_prompt}

STUDENT'S DOCUMENTS CONTENT:
{self.documents_content[:8000]}  # Limit content to avoid token limits

SUMMARIZATION REQUEST: {question}

INSTRUCTIONS: {difficulty_instruction}

Create a well-structured summary that includes:
1. **Main Topics/Concepts** - Key areas covered
2. **Important Definitions** - Essential terms and their meanings
3. **Key Points** - Critical information to remember
4. **Subtopics** - Detailed breakdown of each main area
5. **Examples** - Practical applications or illustrations
6. **Exam Focus** - What's most important for studying

Format your response with clear headings and bullet points for easy studying. Make it comprehensive yet organized for effective exam preparation."""

            response = model.generate_content(prompt)

            return {
                'answer': response.text,
                'source_type': 'PDF',
                'pdf_status': f'Summarized content from {len(self.document_chunks)} document sections',
                'method': 'PDF Summarization (AI Enhanced)',
                'difficulty': difficulty_level,
                'is_summary': True
            }

        except Exception as e:
            return {
                'answer': f"I encountered an error while creating the summary. However, I can tell you that your documents contain {len(self.document_chunks)} sections of content. Please try asking for a summary of a specific topic instead of the entire document.",
                'source_type': 'AI',
                'pdf_status': f'Error in summarization: {str(e)}',
                'method': 'Summarization Fallback',
                'difficulty': difficulty_level,
                'is_summary': True
            }
    
    def _generate_response(self, question, context, source_type="AI", pdf_status="", conversation_history=None, difficulty_level="normal"):
        """Generate intelligent responses with conversation context"""

        try:
            model = genai.GenerativeModel('gemini-1.5-flash')

            # Build conversation context
            context_prompt = ""
            if conversation_history:
                recent_context = conversation_history[-3:]  # Last 3 exchanges
                context_prompt = "\n\nPREVIOUS CONVERSATION:\n"
                for i, chat in enumerate(recent_context):
                    context_prompt += f"Q{i+1}: {chat['question']}\nA{i+1}: {chat['answer'][:200]}...\n"
                context_prompt += "\nCURRENT QUESTION:\n"

            # Adjust difficulty level
            difficulty_instructions = {
                "simple": "Explain in very simple terms, like explaining to a beginner. Use analogies and examples. Avoid technical jargon.",
                "normal": "Provide a clear, comprehensive explanation with examples.",
                "detailed": "Give a thorough, technical explanation with advanced concepts and detailed examples."
            }

            difficulty_instruction = difficulty_instructions.get(difficulty_level, difficulty_instructions["normal"])

            if source_type == "PDF":
                prompt = f"""You are a friendly AI tutor helping a student prepare for academic exams. You remember our conversation and can adapt your explanations.

{context_prompt}

CONTENT FROM STUDENT'S DOCUMENTS:
{context}

Question: {question}

Instructions: {difficulty_instruction}

Based on the document content and our conversation, provide a helpful answer. If this is a follow-up question asking for simplification or clarification, adjust your explanation accordingly. Always be encouraging and supportive."""

                response = model.generate_content(prompt)
                return {
                    'answer': response.text,
                    'source_type': source_type,
                    'pdf_status': pdf_status,
                    'method': 'Your PDF Documents (Conversational)',
                    'difficulty': difficulty_level
                }
            else:
                prompt = f"""You are a friendly AI tutor helping a student prepare for academic exams. You remember our conversation and can adapt your explanations.

{context_prompt}

Question: {question}

Instructions: {difficulty_instruction}

Provide a helpful answer that builds on our conversation. If this is a follow-up question asking for simplification or clarification, adjust your explanation accordingly. Always be encouraging and supportive for exam preparation."""

                response = model.generate_content(prompt)
                return {
                    'answer': response.text,
                    'source_type': source_type,
                    'pdf_status': pdf_status,
                    'method': 'AI Tutor (Conversational)',
                    'difficulty': difficulty_level
                }
            
        except:
            # Fallback responses
            fallback_answers = {
                "machine learning": """Machine learning is a branch of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed for every task.

**Core Concept:**
Instead of writing specific instructions for every scenario, we show computers examples and let them figure out patterns and make decisions on their own.

**Three Main Types:**

1. **Supervised Learning** - Learning with examples and correct answers
   - Like studying with answer keys
   - Examples: Email spam detection, medical diagnosis, price prediction

2. **Unsupervised Learning** - Finding patterns without guidance  
   - Like exploring data to discover hidden insights
   - Examples: Customer segmentation, anomaly detection

3. **Reinforcement Learning** - Learning through trial and error
   - Like learning to play a game through practice
   - Examples: Game playing AI, robotics, autonomous vehicles

**Real-World Applications:**
- Netflix recommendations
- Voice assistants (Siri, Alexa)
- Photo tagging on social media
- GPS route optimization
- Fraud detection in banking

**How It Works:**
1. Collect relevant data
2. Choose appropriate algorithm
3. Train the model on data
4. Test performance
5. Deploy for predictions

Machine learning is transforming every industry by automating decision-making and finding patterns humans might miss!""",

                "hyperplane": """A hyperplane is a fundamental concept in mathematics and machine learning that's easier to understand than it sounds!

**Simple Definition:**
A hyperplane is a flat surface that divides space into two parts. Think of it as:
- In 2D: A line divides a plane into two halves
- In 3D: A plane divides space into two regions  
- In higher dimensions: A hyperplane does the same thing

**Visual Analogy:**
Imagine stretching a large sheet across a room from wall to wall. That sheet divides the room into two spaces (above and below). That's essentially what a hyperplane does in higher dimensions.

**Why Hyperplanes Matter:**

1. **Machine Learning Classification:**
   - Support Vector Machines use hyperplanes as decision boundaries
   - They separate different classes of data
   - Example: Separating emails into "spam" vs "not spam"

2. **Mathematical Properties:**
   - Always one dimension less than the space it's in
   - Defined by equation: w₁x₁ + w₂x₂ + ... + wₙxₙ + b = 0
   - Perfectly flat (no curves)

**Real-World Examples:**
- Credit scoring: Separate "approve" from "deny" based on income, credit score, etc.
- Medical diagnosis: Distinguish "healthy" from "disease" based on test results
- Image recognition: Classify different objects in photos

The beauty of hyperplanes is they provide a clean, mathematical way to make decisions in complex, multi-dimensional problems!"""
            }
            
            question_lower = question.lower()
            for key, answer in fallback_answers.items():
                if key in question_lower:
                    return {
                        'answer': answer,
                        'source_type': source_type,
                        'pdf_status': pdf_status,
                        'method': 'Knowledge Base',
                        'difficulty': difficulty_level
                    }
            
            return {
                'answer': f"I'd be happy to help explain {question}! Could you be more specific about what aspect you'd like to know? I can provide detailed explanations on any topic.",
                'source_type': source_type,
                'pdf_status': pdf_status,
                'method': 'Interactive Assistant',
                'difficulty': difficulty_level
            }

def main():
    # Suppress Streamlit warnings
    st.set_page_config(
        page_title="🧠 PDF AI Assistant",
        layout="wide",
        initial_sidebar_state="collapsed"
    )

    # Add custom CSS to remove any white backgrounds
    st.markdown("""
    <style>
    .stContainer > div {
        background-color: transparent !important;
    }
    div[data-testid="stMarkdownContainer"] {
        background-color: transparent !important;
    }
    .element-container {
        background-color: transparent !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Hide Streamlit style
    hide_streamlit_style = """
    <style>
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {display:none;}
    </style>
    """
    st.markdown(hide_streamlit_style, unsafe_allow_html=True)

    # Check API key with better error handling
    try:
        if not GEMINI_API_KEY:
            st.error("❌ **GEMINI_API_KEY not found!** Please add your API key to continue.")
            st.info("💡 Add your Gemini API key to the `key.env` file or Streamlit secrets.")
            st.code('GEMINI_API_KEY=your_api_key_here', language='bash')
            st.stop()

        # Test API key
        genai.configure(api_key=GEMINI_API_KEY)

    except Exception as e:
        st.error(f"❌ **API Configuration Error:** {str(e)}")
        st.info("💡 Please check your API key and try again.")
        st.stop()

    st.title("🧠 PDF AI Assistant")
    st.markdown("**Upload PDFs and ask questions - I'll search your documents first, then use AI knowledge**")

    # Status indicator
    st.success("✅ **System Status:** All components loaded successfully!")

    # Initialize with comprehensive error handling
    try:
        if 'assistant' not in st.session_state:
            with st.spinner("🔧 Initializing AI Assistant..."):
                st.session_state.assistant = PDFAssistant()
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = []

        # Verify assistant is working
        if st.session_state.assistant.model is None and SENTENCE_TRANSFORMER_AVAILABLE:
            st.warning("⚠️ **Embedding model not loaded** - PDF search may be limited, but AI chat will work!")
        elif not SENTENCE_TRANSFORMER_AVAILABLE:
            st.warning("⚠️ **SentenceTransformer not available** - PDF search disabled, but AI chat will work!")

    except Exception as e:
        st.error(f"❌ **Initialization Error:** {str(e)}")
        st.info("💡 Please refresh the page and try again.")
        st.code(f"Error details: {str(e)}", language='text')
        st.stop()
    
    # Document upload
    st.header("📚 Upload Your Documents")
    uploaded_files = st.file_uploader("Choose PDF files", type="pdf", accept_multiple_files=True)
    
    if uploaded_files:
        with st.spinner("Processing PDFs..."):
            try:
                success = st.session_state.assistant.add_documents(uploaded_files)
                if success:
                    st.success(f"✅ Successfully processed {len(uploaded_files)} PDFs")
                    if st.session_state.assistant.model is None:
                        st.info("📝 **Note:** PDF search is limited, but content is available for AI responses!")
                else:
                    st.error("❌ Could not process PDFs")
            except Exception as e:
                st.error(f"❌ **PDF Processing Error:** {str(e)}")
                st.info("💡 Please try uploading different PDF files.")
    
    # Main interface
    st.header("💬 Chat with Your AI Tutor")

    # Enhanced example questions with summarization
    st.subheader("📝 Quick Questions")
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("🤖 What is machine learning?"):
            st.session_state.current_q = "What is machine learning?"
    with col2:
        if st.button("🔍 What is a hyperplane?"):
            st.session_state.current_q = "What is a hyperplane?"
    with col3:
        if st.button("📊 Explain neural networks"):
            st.session_state.current_q = "How do neural networks work?"

    # Summarization quick buttons
    if st.session_state.assistant.has_documents:
        st.subheader("📋 Document Summaries")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            if st.button("📖 Summarize entire PDF"):
                st.session_state.current_q = "Please provide a comprehensive summary of all the content in my uploaded documents"
        with col2:
            if st.button("🎯 Key points only"):
                st.session_state.current_q = "Give me the key points and main concepts from my documents"
        with col3:
            if st.button("📚 Topic overview"):
                st.session_state.current_q = "Provide an overview of all topics covered in my documents with subtopics"
        with col4:
            if st.button("🔍 Exam focus"):
                st.session_state.current_q = "What are the most important points from my documents for exam preparation?"
    
    # Difficulty level selector
    st.subheader("🎓 Exam Preparation Mode")
    difficulty = st.selectbox(
        "Choose explanation level:",
        ["simple", "normal", "detailed"],
        index=1,
        format_func=lambda x: {
            "simple": "🟢 Simple (Beginner-friendly)",
            "normal": "🟡 Normal (Standard explanation)",
            "detailed": "🔴 Detailed (Advanced/Technical)"
        }[x]
    )

    # Enhanced question input with chat-like interface
    st.subheader("💭 Chat Interface")
    question = st.text_area(
        "Type your message:",
        value=st.session_state.get('current_q', ''),
        placeholder="💬 Chat with me! Ask questions, request definitions, or ask me to summarize topics/PDFs...\n\nExamples:\n• 'What is supervised learning?'\n• 'Summarize the entire PDF'\n• 'Give me key points from chapter 3'\n• 'Explain this in simple terms'",
        height=100
    )

    # Enhanced quick follow-up buttons
    if st.session_state.chat_history:
        st.write("**💬 Quick Chat Responses:**")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            if st.button("📝 Simplify this"):
                st.session_state.current_q = "Can you explain that in simpler terms?"
        with col2:
            if st.button("🔍 More details"):
                st.session_state.current_q = "Can you give me more detailed explanation?"
        with col3:
            if st.button("💡 Give example"):
                st.session_state.current_q = "Can you give me a practical example?"
        with col4:
            if st.button("❓ I'm confused"):
                st.session_state.current_q = "I'm still confused, can you explain differently?"

        # Additional chat-like responses
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            if st.button("🔗 How does this relate?"):
                st.session_state.current_q = "How does this relate to other topics we discussed?"
        with col2:
            if st.button("📋 Summarize our chat"):
                st.session_state.current_q = "Can you summarize what we've discussed so far?"
        with col3:
            if st.button("🎯 Test my knowledge"):
                st.session_state.current_q = "Ask me a question to test my understanding of this topic"
        with col4:
            if st.button("📚 What should I study next?"):
                st.session_state.current_q = "Based on our conversation, what should I study next?"

    if 'current_q' in st.session_state:
        del st.session_state.current_q
    
    if st.button("🚀 Get Answer", type="primary") and question:
        with st.spinner("🔍 Searching PDFs and generating answer..."):

            # Pass conversation history and difficulty level
            response = st.session_state.assistant.answer_question(
                question,
                conversation_history=st.session_state.chat_history,
                difficulty_level=difficulty
            )
            
            # Enhanced status display for chat-like experience
            if response.get('is_summary', False):
                # Special handling for summaries
                if response.get('source_type') == 'PDF':
                    st.success("📋 **SUMMARY GENERATED: From Your PDF Documents**")
                    st.info(f"📊 {response.get('pdf_status', 'Summarized your document content')}")
                else:
                    st.warning("⚠️ **No documents available for summarization**")
            else:
                # Regular question handling
                if st.session_state.assistant.has_documents:
                    if response.get('source_type') == 'PDF':
                        st.success("✅ **ANSWER SOURCE: Your PDF Documents**")
                        st.info("📄 The answer below is based on content found in your uploaded PDFs")

                        # Show which PDF sections were used (only for non-summaries)
                        if not response.get('is_summary', False):
                            pdf_content, _ = st.session_state.assistant.search_documents(question)
                            if pdf_content:
                                with st.expander("📋 View PDF content used for this answer"):
                                    st.text(pdf_content[:1000] + "..." if len(pdf_content) > 1000 else pdf_content)
                    else:
                        st.warning(f"⚠️ **PDF Search Result:** {response.get('pdf_status', 'No relevant content found')}")
                        st.error("❌ **ANSWER SOURCE: AI Knowledge (NOT from your PDFs)**")
                        st.info("💡 The answer below is from AI general knowledge, not your documents")
                else:
                    st.info("📝 **No PDFs uploaded**")
                    st.error("❌ **ANSWER SOURCE: AI Knowledge (No PDFs to search)**")

            # Display answer with enhanced chat-like labeling
            if response.get('is_summary', False):
                st.subheader("📋 Summary")
            elif response.get('source_type') == 'PDF':
                st.subheader("🎯 AI Tutor Response (From Your PDFs)")
            else:
                st.subheader("🎯 AI Tutor Response (From Knowledge Base)")

            # Display response normally without background styling
            st.write(response['answer'])

            # Show detailed source information
            source_type = response.get('source_type', 'AI')
            method = response.get('method', 'Unknown')

            if source_type == 'PDF':
                st.success(f"📚 **Confirmed Source:** Your PDF Documents | **Processing:** AI enhanced explanation")
            else:
                st.info(f"🤖 **Confirmed Source:** AI General Knowledge | **Method:** {method}")
            
            # Add to history with enhanced context for chat
            st.session_state.chat_history.append({
                'question': question,
                'answer': response['answer'],
                'source_type': source_type,
                'method': method,
                'difficulty': response.get('difficulty', 'normal'),
                'timestamp': time.time(),
                'is_summary': response.get('is_summary', False),
                'message_type': 'summary' if response.get('is_summary', False) else 'question'
            })
    
    # Enhanced Chat-like conversation history
    if st.session_state.chat_history:
        st.header("💬 Chat History")

        # Chat statistics
        total_messages = len(st.session_state.chat_history)
        summaries = len([chat for chat in st.session_state.chat_history if chat.get('is_summary', False)])
        questions = total_messages - summaries

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("💬 Total Messages", total_messages)
        with col2:
            st.metric("❓ Questions", questions)
        with col3:
            st.metric("📋 Summaries", summaries)

        # Show recent conversations in chat-like format
        st.subheader("Recent Conversation")
        for i, chat in enumerate(st.session_state.chat_history[-5:]):  # Show last 5
            difficulty_emoji = {"simple": "🟢", "normal": "🟡", "detailed": "🔴"}
            difficulty_level = chat.get('difficulty', 'normal')
            is_summary = chat.get('is_summary', False)

            # Different icons for different message types
            if is_summary:
                icon = "📋"
                type_label = "Summary"
            else:
                icon = "❓"
                type_label = "Question"

            with st.expander(f"{difficulty_emoji.get(difficulty_level, '🟡')} {icon} {type_label}: {chat['question'][:60]}..."):
                # User message
                st.write(f"**You:** {chat['question']}")

                # AI response
                answer_preview = chat['answer'][:300]
                ellipsis = '...' if len(chat['answer']) > 300 else ''
                st.write(f"**AI Tutor:** {answer_preview}{ellipsis}")

                source_type = chat.get('source_type', 'AI')
                method = chat.get('method', 'Unknown')

                col1, col2 = st.columns(2)
                with col1:
                    if source_type == 'PDF':
                        st.success(f"📚 Source: Your PDFs")
                    else:
                        st.info(f"🤖 Source: AI Knowledge")

                with col2:
                    st.caption(f"Level: {difficulty_level.title()} | Method: {method}")

                # Enhanced action buttons for chat
                subcol1, subcol2, subcol3, subcol4 = st.columns(4)
                with subcol1:
                    if st.button(f"🔄 Continue", key=f"continue_{i}"):
                        st.session_state.current_q = chat['question']
                        st.rerun()
                with subcol2:
                    if st.button(f"📝 Simplify", key=f"simple_{i}"):
                        question_text = chat['question']
                        st.session_state.current_q = f"Can you explain '{question_text}' in simpler terms?"
                        st.rerun()
                with subcol3:
                    if st.button(f"🔍 Expand", key=f"detail_{i}"):
                        question_text = chat['question']
                        st.session_state.current_q = f"Can you give more details about '{question_text}'?"
                        st.rerun()
                with subcol4:
                    if st.button(f"🔗 Related", key=f"related_{i}"):
                        question_text = chat['question']
                        st.session_state.current_q = f"What topics are related to '{question_text}'?"
                        st.rerun()

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ Clear History"):
                st.session_state.chat_history = []
                st.rerun()
        with col2:
            if st.button("📊 Study Summary"):
                st.session_state.show_summary = True
                st.rerun()

        # Study summary
        if st.session_state.get('show_summary', False):
            st.subheader("📊 Your Study Session Summary")
            total_questions = len(st.session_state.chat_history)
            pdf_questions = len([q for q in st.session_state.chat_history if q.get('source_type') == 'PDF'])
            ai_questions = total_questions - pdf_questions

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Questions", total_questions)
            with col2:
                st.metric("From Your PDFs", pdf_questions)
            with col3:
                st.metric("From AI Knowledge", ai_questions)

            # Difficulty breakdown
            difficulty_counts = {}
            for chat in st.session_state.chat_history:
                diff = chat.get('difficulty', 'normal')
                difficulty_counts[diff] = difficulty_counts.get(diff, 0) + 1

            st.write("**Question Difficulty Levels:**")
            for diff, count in difficulty_counts.items():
                emoji = {"simple": "🟢", "normal": "🟡", "detailed": "🔴"}
                st.write(f"{emoji.get(diff, '🟡')} {diff.title()}: {count} questions")

            if st.button("❌ Close Summary"):
                st.session_state.show_summary = False
                st.rerun()

if __name__ == "__main__":
    main()

