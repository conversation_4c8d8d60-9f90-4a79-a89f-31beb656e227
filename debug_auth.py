#!/usr/bin/env python3
"""Debug authentication issues"""

from database_manager import DatabaseManager
import bcrypt

def debug_authentication():
    db = DatabaseManager()
    
    # Check the newly created user
    query = "SELECT id, name, email, password_hash, role, is_active FROM users WHERE email = %s"
    result = db.postgres.execute_query(query, ('<EMAIL>',), fetch=True)
    
    if result:
        user = result[0]
        print(f"User found in database:")
        print(f"  ID: {user['id']}")
        print(f"  Name: {user['name']}")
        print(f"  Email: {user['email']}")
        print(f"  Role: {user['role']}")
        print(f"  Is Active: {user['is_active']}")
        print(f"  Password hash length: {len(user['password_hash'])}")
        
        # Test password manually
        test_password = 'newpass123'
        stored_hash = user['password_hash']
        
        try:
            is_valid = bcrypt.checkpw(test_password.encode('utf-8'), stored_hash.encode('utf-8'))
            print(f"  Password verification: {is_valid}")
        except Exception as e:
            print(f"  Password verification error: {e}")
            
        # Test the full authentication query
        auth_query = "SELECT id, name, email, password_hash, role FROM users WHERE email = %s AND is_active = TRUE"
        auth_result = db.postgres.execute_query(auth_query, ('<EMAIL>',), fetch=True)
        
        if auth_result:
            print("  ✅ User found with is_active = TRUE")
            auth_user = auth_result[0]
            try:
                is_auth_valid = bcrypt.checkpw(test_password.encode('utf-8'), auth_user['password_hash'].encode('utf-8'))
                print(f"  Authentication result: {is_auth_valid}")
            except Exception as e:
                print(f"  Authentication error: {e}")
        else:
            print("  ❌ User NOT found with is_active = TRUE")
    else:
        print("❌ User not found in database")

if __name__ == "__main__":
    debug_authentication()
