#!/usr/bin/env python3
"""
Dental Named Entity Recognition (NER) and Knowledge Graph (KG) Builder
Implements the ADDSS requirements for dental entity extraction and relationship building
"""

import spacy
import networkx as nx
import re
import json
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass
from collections import defaultdict
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Load spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    print("Please install the English model: python -m spacy download en_core_web_sm")
    nlp = None

@dataclass
class DentalEntity:
    """Represents a dental entity with type and metadata"""
    text: str
    entity_type: str
    start: int
    end: int
    confidence: float = 1.0
    synonyms: List[str] = None
    
    def __post_init__(self):
        if self.synonyms is None:
            self.synonyms = []

@dataclass
class DentalRelation:
    """Represents a relationship between dental entities"""
    head_entity: str
    relation_type: str
    tail_entity: str
    confidence: float = 1.0
    source_text: str = ""

class DentalNER:
    """Named Entity Recognition for Dental Domain"""
    
    def __init__(self):
        # Dental entity patterns from ADDSS specification
        self.dental_patterns = {
            'DISEASE': [
                'gingivitis', 'periodontitis', 'dental caries', 'tooth decay', 'cavities',
                'apical lesion', 'pulpitis', 'abscess', 'periodontal disease', 'gum disease',
                'oral cancer', 'leukoplakia', 'lichen planus', 'candidiasis', 'thrush',
                'dry socket', 'alveolar osteitis', 'impacted tooth', 'malocclusion',
                'bruxism', 'teeth grinding', 'temporomandibular disorder', 'tmd'
            ],
            'SYMPTOM': [
                'bleeding gums', 'toothache', 'tooth pain', 'sensitivity to cold',
                'sensitivity to hot', 'swelling', 'bad breath', 'halitosis',
                'loose teeth', 'gum recession', 'jaw pain', 'clicking jaw',
                'difficulty chewing', 'dry mouth', 'xerostomia', 'mouth sores',
                'white patches', 'red patches', 'numbness', 'tingling'
            ],
            'PROCEDURE': [
                'root canal treatment', 'tooth extraction', 'dental filling',
                'panoramic radiograph', 'x-ray', 'dental cleaning', 'scaling',
                'crown placement', 'bridge', 'dental implant', 'orthodontic treatment',
                'braces', 'teeth whitening', 'fluoride treatment', 'sealants',
                'biopsy', 'oral surgery', 'wisdom tooth removal', 'endodontic treatment'
            ],
            'MATERIAL': [
                'amalgam', 'composite resin', 'gutta-percha', 'porcelain',
                'gold', 'titanium', 'ceramic', 'acrylic', 'fluoride',
                'local anesthetic', 'lidocaine', 'novocaine', 'dental cement',
                'impression material', 'temporary filling'
            ],
            'INSTRUMENT': [
                'dental probe', 'scaler', 'forceps', 'dental drill', 'handpiece',
                'mirror', 'explorer', 'curette', 'elevator', 'luxator',
                'dental chair', 'suction', 'air-water syringe', 'ultrasonic scaler',
                'laser', 'dental dam', 'matrix band'
            ],
            'ANATOMICAL_STRUCTURE': [
                'enamel', 'dentin', 'pulp', 'root', 'crown', 'mandible', 'maxilla',
                'maxillary sinus', 'alveolar bone', 'periodontal ligament',
                'gingiva', 'gums', 'tongue', 'palate', 'uvula', 'tonsils',
                'salivary glands', 'temporomandibular joint', 'tmj', 'oral cavity'
            ]
        }
        
        # Compile regex patterns for faster matching
        self.compiled_patterns = {}
        for entity_type, patterns in self.dental_patterns.items():
            # Create case-insensitive regex pattern
            pattern = r'\b(?:' + '|'.join(re.escape(p) for p in patterns) + r')\b'
            self.compiled_patterns[entity_type] = re.compile(pattern, re.IGNORECASE)
    
    def extract_entities(self, text: str) -> List[DentalEntity]:
        """Extract dental entities from text using pattern matching and spaCy"""
        entities = []
        
        # Pattern-based extraction for dental terms
        for entity_type, pattern in self.compiled_patterns.items():
            for match in pattern.finditer(text):
                entity = DentalEntity(
                    text=match.group(),
                    entity_type=entity_type,
                    start=match.start(),
                    end=match.end(),
                    confidence=0.9  # High confidence for pattern matches
                )
                entities.append(entity)
        
        # spaCy NER for additional medical entities
        if nlp:
            doc = nlp(text)
            for ent in doc.ents:
                # Filter for relevant entity types
                if ent.label_ in ['PERSON', 'ORG', 'GPE']:
                    continue
                    
                # Check if not already captured by pattern matching
                overlap = any(
                    ent.start_char < e.end and ent.end_char > e.start 
                    for e in entities
                )
                
                if not overlap:
                    # Map spaCy labels to dental categories
                    entity_type = self._map_spacy_label(ent.label_)
                    if entity_type:
                        entity = DentalEntity(
                            text=ent.text,
                            entity_type=entity_type,
                            start=ent.start_char,
                            end=ent.end_char,
                            confidence=0.7  # Lower confidence for spaCy matches
                        )
                        entities.append(entity)
        
        # Remove duplicates and overlaps
        entities = self._remove_overlaps(entities)
        return sorted(entities, key=lambda x: x.start)
    
    def _map_spacy_label(self, spacy_label: str) -> str:
        """Map spaCy entity labels to dental categories"""
        mapping = {
            'DISEASE': 'DISEASE',
            'SYMPTOM': 'SYMPTOM', 
            'TREATMENT': 'PROCEDURE',
            'DRUG': 'MATERIAL',
            'ANATOMY': 'ANATOMICAL_STRUCTURE'
        }
        return mapping.get(spacy_label, None)
    
    def _remove_overlaps(self, entities: List[DentalEntity]) -> List[DentalEntity]:
        """Remove overlapping entities, keeping the one with higher confidence"""
        if not entities:
            return entities
            
        # Sort by start position
        entities.sort(key=lambda x: x.start)
        
        filtered = [entities[0]]
        for entity in entities[1:]:
            last_entity = filtered[-1]
            
            # Check for overlap
            if entity.start < last_entity.end:
                # Keep the one with higher confidence
                if entity.confidence > last_entity.confidence:
                    filtered[-1] = entity
            else:
                filtered.append(entity)
        
        return filtered

class DentalRelationExtractor:
    """Extract relationships between dental entities"""
    
    def __init__(self):
        # Relation patterns from ADDSS specification
        self.relation_patterns = {
            'causes': [
                r'(.+?)\s+(?:causes?|leads? to|results? in|triggers?)\s+(.+)',
                r'(.+?)\s+(?:is caused by|results from|stems from)\s+(.+)',
            ],
            'treats': [
                r'(.+?)\s+(?:treats?|cures?|heals?|addresses?)\s+(.+)',
                r'(.+?)\s+(?:is treated (?:with|by)|is cured (?:with|by))\s+(.+)',
            ],
            'indicates': [
                r'(.+?)\s+(?:indicates?|suggests?|signals?|points to)\s+(.+)',
                r'(.+?)\s+(?:is (?:a )?(?:sign|symptom|indication) of)\s+(.+)',
            ],
            'uses_material': [
                r'(.+?)\s+(?:uses?|utilizes?|employs?|requires?)\s+(.+)',
                r'(.+?)\s+(?:is (?:made|filled|restored) with)\s+(.+)',
            ],
            'part_of': [
                r'(.+?)\s+(?:is (?:a )?part of|belongs to|is located in)\s+(.+)',
                r'(.+?)\s+(?:contains?|includes?|comprises?)\s+(.+)',
            ],
            'has_symptom': [
                r'(.+?)\s+(?:has|shows|exhibits|presents with)\s+(.+)',
                r'(.+?)\s+(?:is characterized by|features)\s+(.+)',
            ]
        }
        
        # Compile patterns
        self.compiled_relation_patterns = {}
        for relation, patterns in self.relation_patterns.items():
            self.compiled_relation_patterns[relation] = [
                re.compile(pattern, re.IGNORECASE) for pattern in patterns
            ]
    
    def extract_relations(self, text: str, entities: List[DentalEntity]) -> List[DentalRelation]:
        """Extract relationships from text given identified entities"""
        relations = []
        
        # Split text into sentences for better relation extraction
        sentences = self._split_sentences(text)
        
        for sentence in sentences:
            # Pattern-based relation extraction
            for relation_type, patterns in self.compiled_relation_patterns.items():
                for pattern in patterns:
                    matches = pattern.finditer(sentence)
                    for match in matches:
                        head_text = match.group(1).strip()
                        tail_text = match.group(2).strip()
                        
                        # Find matching entities
                        head_entity = self._find_entity_match(head_text, entities)
                        tail_entity = self._find_entity_match(tail_text, entities)
                        
                        if head_entity and tail_entity:
                            relation = DentalRelation(
                                head_entity=head_entity.text,
                                relation_type=relation_type,
                                tail_entity=tail_entity.text,
                                confidence=0.8,
                                source_text=sentence
                            )
                            relations.append(relation)
        
        return relations
    
    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _find_entity_match(self, text: str, entities: List[DentalEntity]) -> DentalEntity:
        """Find the best matching entity for given text"""
        text_lower = text.lower()
        
        # Exact match first
        for entity in entities:
            if entity.text.lower() in text_lower:
                return entity
        
        # Partial match
        for entity in entities:
            if any(word in text_lower for word in entity.text.lower().split()):
                return entity
        
        return None

class DentalKnowledgeGraph:
    """Build and manage dental knowledge graph"""
    
    def __init__(self):
        self.graph = nx.DiGraph()
        self.entities = {}
        self.relations = []
    
    def add_entities(self, entities: List[DentalEntity]):
        """Add entities to the knowledge graph"""
        for entity in entities:
            # Normalize entity text
            normalized_text = entity.text.lower().strip()
            
            if normalized_text not in self.entities:
                self.entities[normalized_text] = entity
                self.graph.add_node(
                    normalized_text,
                    entity_type=entity.entity_type,
                    original_text=entity.text,
                    confidence=entity.confidence
                )
    
    def add_relations(self, relations: List[DentalRelation]):
        """Add relations to the knowledge graph"""
        for relation in relations:
            head = relation.head_entity.lower().strip()
            tail = relation.tail_entity.lower().strip()
            
            if head in self.entities and tail in self.entities:
                self.graph.add_edge(
                    head, tail,
                    relation_type=relation.relation_type,
                    confidence=relation.confidence,
                    source_text=relation.source_text
                )
                self.relations.append(relation)
    
    def get_stats(self) -> Dict:
        """Get knowledge graph statistics"""
        entity_counts = defaultdict(int)
        relation_counts = defaultdict(int)
        
        for node, data in self.graph.nodes(data=True):
            entity_counts[data['entity_type']] += 1
        
        for _, _, data in self.graph.edges(data=True):
            relation_counts[data['relation_type']] += 1
        
        return {
            'total_entities': len(self.graph.nodes),
            'total_relations': len(self.graph.edges),
            'entity_types': dict(entity_counts),
            'relation_types': dict(relation_counts)
        }
    
    def visualize_graph(self, output_file: str = None, interactive: bool = True):
        """Visualize the knowledge graph"""
        if interactive:
            return self._create_interactive_plot()
        else:
            return self._create_static_plot(output_file)
    
    def _create_interactive_plot(self):
        """Create interactive plotly visualization"""
        if len(self.graph.nodes) == 0:
            print("No entities to visualize")
            return None
        
        # Create layout
        pos = nx.spring_layout(self.graph, k=3, iterations=50)
        
        # Prepare node data
        node_x = []
        node_y = []
        node_text = []
        node_color = []
        
        color_map = {
            'DISEASE': 'red',
            'SYMPTOM': 'orange', 
            'PROCEDURE': 'blue',
            'MATERIAL': 'green',
            'INSTRUMENT': 'purple',
            'ANATOMICAL_STRUCTURE': 'brown'
        }
        
        for node in self.graph.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)
            
            entity_type = self.graph.nodes[node]['entity_type']
            original_text = self.graph.nodes[node]['original_text']
            
            node_text.append(f"{original_text}<br>Type: {entity_type}")
            node_color.append(color_map.get(entity_type, 'gray'))
        
        # Create node trace
        node_trace = go.Scatter(
            x=node_x, y=node_y,
            mode='markers+text',
            text=[self.graph.nodes[node]['original_text'] for node in self.graph.nodes()],
            textposition="middle center",
            hovertext=node_text,
            hoverinfo='text',
            marker=dict(
                size=20,
                color=node_color,
                line=dict(width=2, color='black')
            )
        )
        
        # Prepare edge data
        edge_x = []
        edge_y = []
        edge_text = []
        
        for edge in self.graph.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
            
            relation_type = self.graph.edges[edge]['relation_type']
            edge_text.append(relation_type)
        
        # Create edge trace
        edge_trace = go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=2, color='gray'),
            hoverinfo='none',
            mode='lines'
        )
        
        # Create figure
        fig = go.Figure(data=[edge_trace, node_trace],
                       layout=go.Layout(
                           title=dict(text='Dental Knowledge Graph', font=dict(size=16)),
                           showlegend=False,
                           hovermode='closest',
                           margin=dict(b=20,l=5,r=5,t=40),
                           annotations=[ dict(
                               text="Dental entities and their relationships",
                               showarrow=False,
                               xref="paper", yref="paper",
                               x=0.005, y=-0.002,
                               xanchor="left", yanchor="bottom",
                               font=dict(color="gray", size=12)
                           )],
                           xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                           yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)
                       ))
        
        return fig
    
    def _create_static_plot(self, output_file: str = None):
        """Create static matplotlib visualization"""
        if len(self.graph.nodes) == 0:
            print("No entities to visualize")
            return None
        
        plt.figure(figsize=(12, 8))
        
        # Create layout
        pos = nx.spring_layout(self.graph, k=2, iterations=50)
        
        # Color nodes by entity type
        color_map = {
            'DISEASE': 'red',
            'SYMPTOM': 'orange',
            'PROCEDURE': 'blue', 
            'MATERIAL': 'green',
            'INSTRUMENT': 'purple',
            'ANATOMICAL_STRUCTURE': 'brown'
        }
        
        node_colors = [color_map.get(self.graph.nodes[node]['entity_type'], 'gray') 
                      for node in self.graph.nodes()]
        
        # Draw the graph
        nx.draw(self.graph, pos, 
                node_color=node_colors,
                node_size=1000,
                font_size=8,
                font_weight='bold',
                arrows=True,
                arrowsize=20,
                edge_color='gray',
                with_labels=True)
        
        # Add edge labels
        edge_labels = nx.get_edge_attributes(self.graph, 'relation_type')
        nx.draw_networkx_edge_labels(self.graph, pos, edge_labels, font_size=6)
        
        plt.title("Dental Knowledge Graph", size=16)
        plt.axis('off')
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
        
        return plt
    
    def export_to_json(self, filename: str):
        """Export knowledge graph to JSON format"""
        data = {
            'entities': [
                {
                    'id': node,
                    'text': self.graph.nodes[node]['original_text'],
                    'type': self.graph.nodes[node]['entity_type'],
                    'confidence': self.graph.nodes[node]['confidence']
                }
                for node in self.graph.nodes()
            ],
            'relations': [
                {
                    'head': edge[0],
                    'tail': edge[1],
                    'relation': self.graph.edges[edge]['relation_type'],
                    'confidence': self.graph.edges[edge]['confidence'],
                    'source': self.graph.edges[edge].get('source_text', '')
                }
                for edge in self.graph.edges()
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Knowledge graph exported to {filename}")

def build_dental_knowledge_graph(text: str) -> DentalKnowledgeGraph:
    """Main function to build dental knowledge graph from text"""
    
    # Initialize components
    ner = DentalNER()
    relation_extractor = DentalRelationExtractor()
    kg = DentalKnowledgeGraph()
    
    print("🧠 Extracting dental entities...")
    entities = ner.extract_entities(text)
    print(f"Found {len(entities)} entities")
    
    print("🔗 Extracting relationships...")
    relations = relation_extractor.extract_relations(text, entities)
    print(f"Found {len(relations)} relationships")
    
    print("🕸️ Building knowledge graph...")
    kg.add_entities(entities)
    kg.add_relations(relations)
    
    stats = kg.get_stats()
    print(f"📊 Knowledge Graph Stats:")
    print(f"  - Total entities: {stats['total_entities']}")
    print(f"  - Total relations: {stats['total_relations']}")
    print(f"  - Entity types: {stats['entity_types']}")
    print(f"  - Relation types: {stats['relation_types']}")
    
    return kg

if __name__ == "__main__":
    # Test with sample dental text
    sample_text = """
    Gingivitis causes bleeding gums and can lead to periodontitis if left untreated.
    Root canal treatment is used to treat infected pulp in the tooth.
    Dental caries indicates tooth decay and requires dental filling with composite resin.
    The enamel is part of the tooth crown and protects the underlying dentin.
    Toothache indicates dental problems and may require extraction using forceps.
    """
    
    kg = build_dental_knowledge_graph(sample_text)
    
    # Export results
    kg.export_to_json("dental_knowledge_graph.json")
    
    # Create visualization
    fig = kg.visualize_graph(interactive=True)
    if fig:
        fig.show()
