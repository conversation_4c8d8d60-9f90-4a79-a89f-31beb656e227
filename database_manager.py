#!/usr/bin/env python3
"""
Database Manager for Dental AI System
Handles connections to PostgreSQL and Neo4j databases
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import hashlib

import psycopg2
from psycopg2.extras import <PERSON>DictCursor
from psycopg2.pool import SimpleConnectionPool
from neo4j import GraphDatabase
import bcrypt

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from database_config import DatabaseConfig
except ImportError:
    # Fallback configuration if config file doesn't exist
    class DatabaseConfig:
        """Database configuration settings"""

        # PostgreSQL settings
        POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
        POSTGRES_PORT = os.getenv('POSTGRES_PORT', '5432')
        POSTGRES_DB = os.getenv('POSTGRES_DB', 'dental_ai')
        POSTGRES_USER = os.getenv('POSTGRES_USER', 'postgres')
        POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'password')

        # Neo4j settings
        NEO4J_URI = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        NEO4J_USER = os.getenv('NEO4J_USER', 'neo4j')
        NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD', 'password')

        @classmethod
        def get_postgres_url(cls):
            return f"postgresql://{cls.POSTGRES_USER}:{cls.POSTGRES_PASSWORD}@{cls.POSTGRES_HOST}:{cls.POSTGRES_PORT}/{cls.POSTGRES_DB}"

class PostgreSQLManager:
    """PostgreSQL database manager for user data, uploads, and chat history"""
    
    def __init__(self):
        self.config = DatabaseConfig()
        self.connection_pool = None
        self._initialize_connection_pool()
    
    def _initialize_connection_pool(self):
        """Initialize PostgreSQL connection pool"""
        try:
            self.connection_pool = SimpleConnectionPool(
                1, 20,  # min and max connections
                host=self.config.POSTGRES_HOST,
                port=self.config.POSTGRES_PORT,
                database=self.config.POSTGRES_DB,
                user=self.config.POSTGRES_USER,
                password=self.config.POSTGRES_PASSWORD
            )
            logger.info("PostgreSQL connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL connection pool: {e}")
            raise
    
    def get_connection(self):
        """Get a connection from the pool"""
        return self.connection_pool.getconn()
    
    def return_connection(self, conn):
        """Return a connection to the pool"""
        self.connection_pool.putconn(conn)
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = False):
        """Execute a query with connection pooling"""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                if fetch:
                    result = cursor.fetchall()
                    conn.commit()  # Commit even when fetching
                    return result
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database query error: {e}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    
    # User Management
    def create_user(self, name: str, email: str, password: str, role: str = 'user') -> int:
        """Create a new user"""
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        query = """
            INSERT INTO users (name, email, password_hash, role, is_active)
            VALUES (%s, %s, %s, %s, %s)
            RETURNING id
        """
        result = self.execute_query(query, (name, email, password_hash, role, True), fetch=True)
        return result[0]['id'] if result else None
    
    def authenticate_user(self, email: str, password: str) -> Optional[Dict]:
        """Authenticate user login"""
        query = "SELECT id, name, email, password_hash, role FROM users WHERE email = %s AND is_active = TRUE"
        result = self.execute_query(query, (email,), fetch=True)
        
        if result and bcrypt.checkpw(password.encode('utf-8'), result[0]['password_hash'].encode('utf-8')):
            user = dict(result[0])
            del user['password_hash']  # Don't return password hash
            return user
        return None
    
    def get_user(self, user_id: int) -> Optional[Dict]:
        """Get user by ID"""
        query = "SELECT id, name, email, role, created_at FROM users WHERE id = %s AND is_active = TRUE"
        result = self.execute_query(query, (user_id,), fetch=True)
        return dict(result[0]) if result else None
    
    # Upload Management
    def create_upload(self, user_id: int, filename: str, original_filename: str, 
                     file_type: str, file_size: int, file_path: str) -> int:
        """Create upload record"""
        query = """
            INSERT INTO uploads (user_id, filename, original_filename, file_type, file_size, file_path)
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING id
        """
        result = self.execute_query(query, (user_id, filename, original_filename, file_type, file_size, file_path), fetch=True)
        return result[0]['id'] if result else None
    
    def update_upload_status(self, upload_id: int, status: str, processing_info: Dict = None):
        """Update upload processing status"""
        query = """
            UPDATE uploads 
            SET status = %s, processing_info = %s, updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """
        processing_json = json.dumps(processing_info) if processing_info else None
        self.execute_query(query, (status, processing_json, upload_id))
    
    def get_user_uploads(self, user_id: int) -> List[Dict]:
        """Get all uploads for a user"""
        query = """
            SELECT id, filename, original_filename, file_type, file_size, status, created_at
            FROM uploads 
            WHERE user_id = %s 
            ORDER BY created_at DESC
        """
        result = self.execute_query(query, (user_id,), fetch=True)
        return [dict(row) for row in result] if result else []
    
    # Chat History
    def save_chat(self, user_id: int, upload_id: Optional[int], question: str, 
                  answer: str, context_type: str = 'general', difficulty_level: str = 'normal',
                  response_time_ms: int = 0) -> int:
        """Save chat interaction"""
        query = """
            INSERT INTO chat_history (user_id, upload_id, question, answer, context_type, difficulty_level, response_time_ms)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """
        result = self.execute_query(query, (user_id, upload_id, question, answer, context_type, difficulty_level, response_time_ms), fetch=True)
        return result[0]['id'] if result else None
    
    def get_chat_history(self, user_id: int, limit: int = 50) -> List[Dict]:
        """Get chat history for a user"""
        query = """
            SELECT ch.*, u.original_filename
            FROM chat_history ch
            LEFT JOIN uploads u ON ch.upload_id = u.id
            WHERE ch.user_id = %s
            ORDER BY ch.created_at DESC
            LIMIT %s
        """
        result = self.execute_query(query, (user_id, limit), fetch=True)
        return [dict(row) for row in result] if result else []
    
    # Knowledge Graph Storage (PostgreSQL backup)
    def save_kg_entities(self, upload_id: int, entities: List[Dict]):
        """Save knowledge graph entities"""
        if not entities:
            return
        
        query = """
            INSERT INTO kg_entities (upload_id, entity_text, entity_type, confidence, start_pos, end_pos)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        values = [
            (upload_id, entity['text'], entity['type'], entity.get('confidence', 1.0), 
             entity.get('start', 0), entity.get('end', 0))
            for entity in entities
        ]
        
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cursor:
                cursor.executemany(query, values)
                conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Error saving KG entities: {e}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    
    def save_kg_relations(self, upload_id: int, relations: List[Dict]):
        """Save knowledge graph relations"""
        if not relations:
            return
        
        query = """
            INSERT INTO kg_relations (upload_id, head_entity, tail_entity, relation_type, confidence, source_text)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        values = [
            (upload_id, rel['head'], rel['tail'], rel['relation'], 
             rel.get('confidence', 1.0), rel.get('source', ''))
            for rel in relations
        ]
        
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cursor:
                cursor.executemany(query, values)
                conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Error saving KG relations: {e}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    
    def get_kg_stats(self, upload_id: int = None) -> Dict:
        """Get knowledge graph statistics"""
        base_query = """
            SELECT 
                COUNT(DISTINCT e.id) as total_entities,
                COUNT(DISTINCT r.id) as total_relations,
                COUNT(DISTINCT e.entity_type) as entity_types,
                COUNT(DISTINCT r.relation_type) as relation_types
            FROM kg_entities e
            FULL OUTER JOIN kg_relations r ON e.upload_id = r.upload_id
        """
        
        if upload_id:
            query = base_query + " WHERE e.upload_id = %s OR r.upload_id = %s"
            params = (upload_id, upload_id)
        else:
            query = base_query
            params = None
        
        result = self.execute_query(query, params, fetch=True)
        return dict(result[0]) if result else {}
    
    def close(self):
        """Close connection pool"""
        if self.connection_pool:
            self.connection_pool.closeall()

class Neo4jManager:
    """Neo4j database manager for knowledge graph operations"""
    
    def __init__(self):
        self.config = DatabaseConfig()
        self.driver = None
        self._initialize_driver()
    
    def _initialize_driver(self):
        """Initialize Neo4j driver"""
        try:
            self.driver = GraphDatabase.driver(
                self.config.NEO4J_URI,
                auth=(self.config.NEO4J_USER, self.config.NEO4J_PASSWORD)
            )
            # Test connection
            with self.driver.session() as session:
                session.run("RETURN 1")
            logger.info("Neo4j driver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Neo4j driver: {e}")
            raise
    
    def close(self):
        """Close Neo4j driver"""
        if self.driver:
            self.driver.close()
    
    def create_entity(self, name: str, entity_type: str, properties: Dict = None):
        """Create an entity node"""
        with self.driver.session() as session:
            props = properties or {}
            props.update({'name': name, 'type': entity_type})
            
            query = """
                MERGE (e:Entity {name: $name, type: $type})
                SET e += $props
                RETURN e
            """
            session.run(query, name=name, type=entity_type, props=props)
    
    def create_relation(self, from_entity: str, to_entity: str, relation_type: str, properties: Dict = None):
        """Create a relationship between entities"""
        with self.driver.session() as session:
            props = properties or {}
            
            query = """
                MATCH (a:Entity {name: $from_entity})
                MATCH (b:Entity {name: $to_entity})
                MERGE (a)-[r:RELATES {type: $relation_type}]->(b)
                SET r += $props
                RETURN r
            """
            session.run(query, from_entity=from_entity, to_entity=to_entity, 
                       relation_type=relation_type, props=props)
    
    def build_knowledge_graph(self, entities: List[Dict], relations: List[Dict], upload_id: int = None):
        """Build complete knowledge graph from entities and relations"""
        with self.driver.session() as session:
            # Clear existing data for this upload if specified
            if upload_id:
                session.run("""
                    MATCH (e:Entity)-[r:RELATES]->(f:Entity)
                    WHERE e.upload_id = $upload_id OR f.upload_id = $upload_id
                    DELETE r
                """, upload_id=upload_id)
                
                session.run("""
                    MATCH (e:Entity {upload_id: $upload_id})
                    DELETE e
                """, upload_id=upload_id)
            
            # Create entities
            for entity in entities:
                props = {
                    'confidence': entity.get('confidence', 1.0),
                    'upload_id': upload_id
                }
                self.create_entity(entity['text'], entity['type'], props)
            
            # Create relations
            for relation in relations:
                props = {
                    'confidence': relation.get('confidence', 1.0),
                    'source_text': relation.get('source', ''),
                    'upload_id': upload_id
                }
                self.create_relation(relation['head'], relation['tail'], relation['relation'], props)
    
    def query_related_entities(self, entity_name: str, max_depth: int = 2) -> List[Dict]:
        """Find entities related to a given entity"""
        with self.driver.session() as session:
            query = """
                MATCH path = (start:Entity {name: $entity_name})-[r:RELATES*1..$max_depth]-(related:Entity)
                RETURN DISTINCT related.name as name, related.type as type, 
                       [rel in relationships(path) | rel.type] as relation_path
                LIMIT 20
            """
            result = session.run(query, entity_name=entity_name, max_depth=max_depth)
            return [dict(record) for record in result]
    
    def find_shortest_path(self, entity1: str, entity2: str) -> List[Dict]:
        """Find shortest path between two entities"""
        with self.driver.session() as session:
            query = """
                MATCH path = shortestPath((a:Entity {name: $entity1})-[r:RELATES*]-(b:Entity {name: $entity2}))
                RETURN [node in nodes(path) | {name: node.name, type: node.type}] as nodes,
                       [rel in relationships(path) | rel.type] as relations
            """
            result = session.run(query, entity1=entity1, entity2=entity2)
            return [dict(record) for record in result]
    
    def get_graph_stats(self) -> Dict:
        """Get knowledge graph statistics"""
        with self.driver.session() as session:
            query = """
                MATCH (e:Entity)
                OPTIONAL MATCH (e)-[r:RELATES]-()
                RETURN 
                    COUNT(DISTINCT e) as total_entities,
                    COUNT(DISTINCT r) as total_relations,
                    COUNT(DISTINCT e.type) as entity_types,
                    COUNT(DISTINCT r.type) as relation_types
            """
            result = session.run(query)
            return dict(result.single()) if result else {}

class DatabaseManager:
    """Unified database manager for both PostgreSQL and Neo4j"""
    
    def __init__(self):
        self.postgres = PostgreSQLManager()
        self.neo4j = Neo4jManager()
    
    def close(self):
        """Close all database connections"""
        self.postgres.close()
        self.neo4j.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

# Test connection function
def test_connections():
    """Test database connections"""
    postgres_ok = False
    neo4j_ok = False

    print("🔧 Testing database connections...")
    print(f"PostgreSQL: {DatabaseConfig.POSTGRES_HOST}:{DatabaseConfig.POSTGRES_PORT}/{DatabaseConfig.POSTGRES_DB}")
    print(f"Neo4j: {DatabaseConfig.NEO4J_URI}")
    print("-" * 50)

    # Test PostgreSQL
    try:
        postgres_manager = PostgreSQLManager()
        result = postgres_manager.execute_query("SELECT version()", fetch=True)
        print(f"✅ PostgreSQL connected: {result[0]['version'][:50]}...")
        postgres_manager.close()
        postgres_ok = True
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print("💡 Make sure PostgreSQL is running and credentials are correct in database_config.py")

    # Test Neo4j
    try:
        neo4j_manager = Neo4jManager()
        stats = neo4j_manager.get_graph_stats()
        print(f"✅ Neo4j connected: {stats}")
        neo4j_manager.close()
        neo4j_ok = True
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        print("💡 Make sure Neo4j is running and credentials are correct in database_config.py")

    if postgres_ok and neo4j_ok:
        print("🎉 All database connections successful!")
        return True
    else:
        print("⚠️ Some database connections failed. Check the setup instructions.")
        return False

if __name__ == "__main__":
    test_connections()
