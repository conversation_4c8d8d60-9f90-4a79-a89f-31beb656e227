# 🗄️ Database Setup Guide for Dental AI System

## 🎯 **What We Built**

You now have a **production-ready database integration layer** for your dental AI system with:

### **✅ PostgreSQL Integration**
- **User Management** (authentication, roles)
- **Upload Tracking** (file metadata, processing status)
- **Chat History** (conversation logging)
- **Knowledge Graph Backup** (entities and relations)
- **Document Chunks** (for RAG with vector embeddings)

### **✅ Neo4j Integration**
- **Advanced Knowledge Graph** (complex relationship queries)
- **Graph Analytics** (shortest paths, related entities)
- **Dental Ontology** (structured medical knowledge)

### **✅ Unified Database Manager**
- **Connection Pooling** for PostgreSQL
- **Error Handling** and graceful fallbacks
- **Batch Operations** for performance
- **Security** with password hashing

---

## 🚀 **Setup Instructions**

### **Step 1: Start Your Databases**

#### **🐘 PostgreSQL Setup:**
1. **Start PostgreSQL** (if not already running)
   - Windows: Start PostgreSQL service
   - Check if running: `netstat -an | findstr 5432`

2. **Create Database:**
   ```sql
   -- Connect to PostgreSQL as superuser
   CREATE DATABASE dental_ai;
   ```

3. **Update Password:**
   - Edit `database_config.py`
   - Change `POSTGRES_PASSWORD = 'your_postgres_password'` to your actual password

#### **🕸️ Neo4j Setup:**
1. **Start Neo4j** (if not already running)
   - Open Neo4j Desktop or start Neo4j service
   - Check if running: `netstat -an | findstr 7687`

2. **Access Neo4j Browser:**
   - Open: http://localhost:7474
   - Login with your Neo4j credentials

3. **Update Password:**
   - Edit `database_config.py`
   - Change `NEO4J_PASSWORD = 'your_neo4j_password'` to your actual password

### **Step 2: Create Database Schema**

#### **🐘 PostgreSQL Tables:**
```bash
# Run the SQL setup script in your PostgreSQL database
# Use pgAdmin, psql, or any PostgreSQL client
```

Copy and run the contents of `database_setup.sql` in your PostgreSQL database.

#### **🕸️ Neo4j Knowledge Graph:**
```bash
# Run the Cypher setup script in Neo4j Browser
# Copy and paste commands from neo4j_setup.cypher
```

### **Step 3: Test Connections**

```bash
python database_manager.py
```

**Expected Output:**
```
✅ PostgreSQL connected: PostgreSQL 15.x on x86_64...
✅ Neo4j connected: {'total_entities': 21, 'total_relations': 17, ...}
🎉 All database connections successful!
```

---

## 📊 **Database Schema Overview**

### **PostgreSQL Tables:**

| Table | Purpose | Key Fields |
|-------|---------|------------|
| `users` | User authentication | id, name, email, password_hash, role |
| `uploads` | File upload tracking | id, user_id, filename, file_type, status |
| `chat_history` | Conversation logs | id, user_id, question, answer, context_type |
| `kg_entities` | Knowledge graph entities | id, upload_id, entity_text, entity_type |
| `kg_relations` | Knowledge graph relations | id, head_entity, tail_entity, relation_type |
| `document_chunks` | RAG text chunks | id, upload_id, chunk_text, embedding |

### **Neo4j Graph Schema:**

| Node Type | Properties | Example |
|-----------|------------|---------|
| `Entity` | name, type, confidence, upload_id | "Gingivitis" (DISEASE) |

| Relationship | Properties | Example |
|--------------|------------|---------|
| `RELATES` | type, confidence, source_text | (Gingivitis)-[causes]->(Bleeding Gums) |

---

## 🔧 **Usage Examples**

### **User Management:**
```python
from database_manager import DatabaseManager

with DatabaseManager() as db:
    # Create user
    user_id = db.postgres.create_user("Dr. Smith", "<EMAIL>", "password123", "dentist")
    
    # Authenticate user
    user = db.postgres.authenticate_user("<EMAIL>", "password123")
    print(f"Welcome {user['name']}!")
```

### **Upload Tracking:**
```python
# Track file upload
upload_id = db.postgres.create_upload(
    user_id=1,
    filename="dental_textbook.pdf",
    original_filename="Dental Pathology.pdf",
    file_type="pdf",
    file_size=2048000,
    file_path="/uploads/dental_textbook.pdf"
)

# Update processing status
db.postgres.update_upload_status(upload_id, "completed", {"entities": 65, "relations": 34})
```

### **Knowledge Graph Operations:**
```python
# Build knowledge graph in Neo4j
entities = [
    {"text": "Gingivitis", "type": "DISEASE", "confidence": 0.9},
    {"text": "Bleeding Gums", "type": "SYMPTOM", "confidence": 0.9}
]

relations = [
    {"head": "Gingivitis", "tail": "Bleeding Gums", "relation": "causes", "confidence": 0.8}
]

db.neo4j.build_knowledge_graph(entities, relations, upload_id=1)

# Query related entities
related = db.neo4j.query_related_entities("Gingivitis")
print(f"Related to Gingivitis: {related}")
```

### **Chat History:**
```python
# Save chat interaction
chat_id = db.postgres.save_chat(
    user_id=1,
    upload_id=1,
    question="What causes bleeding gums?",
    answer="Bleeding gums are primarily caused by gingivitis...",
    context_type="knowledge_graph",
    difficulty_level="normal",
    response_time_ms=1500
)
```

---

## 🔍 **Useful Database Queries**

### **PostgreSQL Queries:**

```sql
-- Get user upload statistics
SELECT u.name, COUNT(up.id) as total_uploads, 
       COUNT(CASE WHEN up.status = 'completed' THEN 1 END) as completed_uploads
FROM users u
LEFT JOIN uploads up ON u.id = up.user_id
GROUP BY u.id, u.name;

-- Get knowledge graph statistics by upload
SELECT up.original_filename,
       COUNT(DISTINCT ke.id) as entities,
       COUNT(DISTINCT kr.id) as relations
FROM uploads up
LEFT JOIN kg_entities ke ON up.id = ke.upload_id
LEFT JOIN kg_relations kr ON up.id = kr.upload_id
WHERE up.status = 'completed'
GROUP BY up.id, up.original_filename;
```

### **Neo4j Queries:**

```cypher
// Find all diseases and their symptoms
MATCH (d:Entity {type: "DISEASE"})-[r:RELATES {type: "causes"}]->(s:Entity {type: "SYMPTOM"})
RETURN d.name as Disease, s.name as Symptom;

// Find treatment procedures for diseases
MATCH (p:Entity {type: "PROCEDURE"})-[r:RELATES {type: "treats"}]->(d:Entity {type: "DISEASE"})
RETURN p.name as Procedure, d.name as Treats;

// Find shortest path between entities
MATCH path = shortestPath((a:Entity {name: "Gingivitis"})-[*]-(b:Entity {name: "Dental Cleaning"}))
RETURN path;
```

---

## 🚀 **Next Steps**

### **1. Start Your Databases**
- Start PostgreSQL and Neo4j services
- Update passwords in `database_config.py`
- Run `python database_manager.py` to test connections

### **2. Create Database Schema**
- Run `database_setup.sql` in PostgreSQL
- Run `neo4j_setup.cypher` in Neo4j Browser

### **3. Integrate with AI Assistant**
- The database layer is ready to be integrated with your `fixed_ai_assistant.py`
- User authentication, upload tracking, and knowledge graph storage are all ready

### **4. Production Deployment**
- For production, consider cloud databases:
  - **PostgreSQL**: AWS RDS, Google Cloud SQL
  - **Neo4j**: Neo4j AuraDB, AWS Neptune

---

## 🎉 **Success!**

You now have a **production-ready database architecture** for your dental AI system! The integration layer handles:

- ✅ **User Management** with secure authentication
- ✅ **File Upload Tracking** with processing status
- ✅ **Chat History** for conversation continuity
- ✅ **Knowledge Graph Storage** in both PostgreSQL and Neo4j
- ✅ **Advanced Graph Queries** for complex dental relationships

**Ready to integrate with your AI assistant!** 🦷🧠

---

*Built with ❤️ for scalable dental AI systems*
