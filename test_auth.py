#!/usr/bin/env python3
"""Test authentication system"""

from database_manager import DatabaseManager

def test_authentication():
    db = DatabaseManager()
    
    # Test with existing sample users
    test_users = [
        ('<EMAIL>', 'admin123'),
        ('<EMAIL>', 'dentist123'),
        ('<EMAIL>', 'student123'),
        ('<EMAIL>', 'student123')
    ]
    
    print("🧪 Testing authentication with sample users:")
    print("-" * 50)
    
    for email, password in test_users:
        try:
            user = db.postgres.authenticate_user(email, password)
            if user:
                print(f"✅ {email} - SUCCESS: {user['name']} ({user['role']})")
            else:
                print(f"❌ {email} - FAILED")
        except Exception as e:
            print(f"❌ {email} - ERROR: {e}")
    
    print("\n🔧 Testing user creation:")
    print("-" * 50)
    
    # Test creating a new user
    try:
        user_id = db.postgres.create_user(
            name='New Dentist',
            email='<EMAIL>',
            password='newpass123',
            role='dentist'
        )
        
        if user_id:
            print(f"✅ User created with ID: {user_id}")
            
            # Test authentication with new user
            user = db.postgres.authenticate_user('<EMAIL>', 'newpass123')
            if user:
                print(f"✅ New user authentication successful: {user['name']}")
            else:
                print("❌ New user authentication failed")
        else:
            print("❌ User creation failed")
            
    except Exception as e:
        print(f"❌ User creation error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_authentication()
