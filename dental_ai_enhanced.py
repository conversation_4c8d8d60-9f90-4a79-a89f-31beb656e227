#!/usr/bin/env python3
"""
Enhanced Dental AI Assistant with PostgreSQL + Neo4j Integration
Combines the existing AI assistant with production-ready database capabilities
"""

import streamlit as st
import os
import tempfile
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
import logging

# Import existing AI assistant components
from fixed_ai_assistant import PDFAssistant

# Import database components
try:
    from database_manager import DatabaseManager
    DATABASE_AVAILABLE = True
except ImportError as e:
    st.warning(f"Database integration not available: {e}")
    DATABASE_AVAILABLE = False

# Import dental NER and KG
try:
    from dental_ner_kg import build_dental_knowledge_graph
    DENTAL_NER_AVAILABLE = True
except ImportError as e:
    st.warning(f"Dental NER not available: {e}")
    DENTAL_NER_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedDentalAI:
    """Enhanced Dental AI with database integration"""
    
    def __init__(self):
        self.pdf_assistant = PDFAssistant()
        self.db_manager = None
        self.current_user = None
        self.current_upload_id = None
        
        # Initialize database connection if available
        if DATABASE_AVAILABLE:
            try:
                self.db_manager = DatabaseManager()
                logger.info("Database manager initialized")
            except Exception as e:
                logger.error(f"Failed to initialize database: {e}")
                self.db_manager = None
    
    def authenticate_user(self, email: str, password: str) -> bool:
        """Authenticate user login"""
        if not self.db_manager:
            return True  # Skip authentication if database not available
        
        try:
            user = self.db_manager.postgres.authenticate_user(email, password)
            if user:
                self.current_user = user
                st.session_state.current_user = user
                return True
            return False
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def create_user(self, name: str, email: str, password: str, role: str = 'user') -> bool:
        """Create new user account"""
        if not self.db_manager:
            return True  # Skip if database not available
        
        try:
            user_id = self.db_manager.postgres.create_user(name, email, password, role)
            return user_id is not None
        except Exception as e:
            logger.error(f"User creation error: {e}")
            return False
    
    def track_upload(self, uploaded_file, file_path: str) -> Optional[int]:
        """Track file upload in database"""
        if not self.db_manager or not self.current_user:
            return None
        
        try:
            upload_id = self.db_manager.postgres.create_upload(
                user_id=self.current_user['id'],
                filename=os.path.basename(file_path),
                original_filename=uploaded_file.name,
                file_type=uploaded_file.type.split('/')[-1] if uploaded_file.type else 'unknown',
                file_size=uploaded_file.size,
                file_path=file_path
            )
            self.current_upload_id = upload_id
            return upload_id
        except Exception as e:
            logger.error(f"Upload tracking error: {e}")
            return None
    
    def update_upload_status(self, upload_id: int, status: str, processing_info: Dict = None):
        """Update upload processing status"""
        if not self.db_manager:
            return
        
        try:
            self.db_manager.postgres.update_upload_status(upload_id, status, processing_info)
        except Exception as e:
            logger.error(f"Upload status update error: {e}")
    
    def save_knowledge_graph(self, upload_id: int, kg_data: Dict):
        """Save knowledge graph to both PostgreSQL and Neo4j"""
        if not self.db_manager:
            return
        
        try:
            # Extract entities and relations from knowledge graph
            entities = []
            relations = []
            
            if hasattr(kg_data, 'graph'):
                # From DentalKnowledgeGraph object
                for node, data in kg_data.graph.nodes(data=True):
                    entities.append({
                        'text': data['original_text'],
                        'type': data['entity_type'],
                        'confidence': data['confidence']
                    })
                
                for edge in kg_data.graph.edges(data=True):
                    head, tail, data = edge
                    relations.append({
                        'head': head,
                        'tail': tail,
                        'relation': data['relation_type'],
                        'confidence': data['confidence'],
                        'source': data.get('source_text', '')
                    })
            
            # Save to PostgreSQL (backup/search)
            self.db_manager.postgres.save_kg_entities(upload_id, entities)
            self.db_manager.postgres.save_kg_relations(upload_id, relations)
            
            # Save to Neo4j (advanced queries)
            self.db_manager.neo4j.build_knowledge_graph(entities, relations, upload_id)
            
            logger.info(f"Knowledge graph saved: {len(entities)} entities, {len(relations)} relations")
            
        except Exception as e:
            logger.error(f"Knowledge graph save error: {e}")
    
    def save_chat_interaction(self, question: str, answer: str, context_type: str = 'general', 
                            difficulty_level: str = 'normal', response_time_ms: int = 0):
        """Save chat interaction to database"""
        if not self.db_manager or not self.current_user:
            return
        
        try:
            chat_id = self.db_manager.postgres.save_chat(
                user_id=self.current_user['id'],
                upload_id=self.current_upload_id,
                question=question,
                answer=answer,
                context_type=context_type,
                difficulty_level=difficulty_level,
                response_time_ms=response_time_ms
            )
            return chat_id
        except Exception as e:
            logger.error(f"Chat save error: {e}")
            return None
    
    def get_user_uploads(self) -> List[Dict]:
        """Get user's upload history"""
        if not self.db_manager or not self.current_user:
            return []
        
        try:
            return self.db_manager.postgres.get_user_uploads(self.current_user['id'])
        except Exception as e:
            logger.error(f"Get uploads error: {e}")
            return []
    
    def get_chat_history(self, limit: int = 10) -> List[Dict]:
        """Get user's chat history"""
        if not self.db_manager or not self.current_user:
            return []
        
        try:
            return self.db_manager.postgres.get_chat_history(self.current_user['id'], limit)
        except Exception as e:
            logger.error(f"Get chat history error: {e}")
            return []
    
    def query_knowledge_graph(self, entity_name: str) -> List[Dict]:
        """Query Neo4j knowledge graph for related entities"""
        if not self.db_manager:
            return []
        
        try:
            return self.db_manager.neo4j.query_related_entities(entity_name)
        except Exception as e:
            logger.error(f"Knowledge graph query error: {e}")
            return []
    
    def process_documents_with_tracking(self, uploaded_files):
        """Process documents with database tracking"""
        start_time = time.time()
        
        # Process documents using existing PDF assistant
        success = self.pdf_assistant.add_documents(uploaded_files)
        
        if success and self.current_upload_id:
            # Build knowledge graph if dental NER is available
            if DENTAL_NER_AVAILABLE and self.pdf_assistant.documents_content:
                try:
                    st.info("🧠 Building knowledge graph...")
                    kg = build_dental_knowledge_graph(self.pdf_assistant.documents_content)
                    
                    # Save knowledge graph to databases
                    self.save_knowledge_graph(self.current_upload_id, kg)
                    
                    # Update upload status
                    kg_stats = kg.get_stats()
                    processing_info = {
                        'entities': kg_stats['total_entities'],
                        'relations': kg_stats['total_relations'],
                        'entity_types': kg_stats['entity_types'],
                        'relation_types': kg_stats['relation_types'],
                        'processing_time_seconds': time.time() - start_time
                    }
                    
                    self.update_upload_status(self.current_upload_id, 'completed', processing_info)
                    
                    st.success(f"✅ Knowledge graph built: {kg_stats['total_entities']} entities, {kg_stats['total_relations']} relations")
                    
                except Exception as e:
                    logger.error(f"Knowledge graph processing error: {e}")
                    self.update_upload_status(self.current_upload_id, 'failed', {'error': str(e)})
            else:
                # Update status without knowledge graph
                processing_info = {
                    'chunks': len(self.pdf_assistant.document_chunks),
                    'processing_time_seconds': time.time() - start_time
                }
                self.update_upload_status(self.current_upload_id, 'completed', processing_info)
        
        return success
    
    def answer_question_with_tracking(self, question: str, difficulty_level: str = 'normal'):
        """Answer question with database tracking"""
        start_time = time.time()
        
        # Get answer from PDF assistant
        response = self.pdf_assistant.answer_question(question, difficulty_level=difficulty_level)
        
        # Determine context type
        context_type = 'document' if self.pdf_assistant.has_documents else 'general'
        if self.db_manager and 'Knowledge Graph Context' in response.get('answer', ''):
            context_type = 'knowledge_graph'
        
        # Save chat interaction
        response_time_ms = int((time.time() - start_time) * 1000)
        self.save_chat_interaction(
            question=question,
            answer=response.get('answer', ''),
            context_type=context_type,
            difficulty_level=difficulty_level,
            response_time_ms=response_time_ms
        )
        
        return response
    
    def close(self):
        """Close database connections"""
        if self.db_manager:
            self.db_manager.close()

def show_login_page():
    """Show login/registration page"""
    st.title("🦷 Dental AI - Login")
    
    tab1, tab2 = st.tabs(["Login", "Register"])
    
    with tab1:
        st.subheader("Login to Your Account")
        email = st.text_input("Email", key="login_email")
        password = st.text_input("Password", type="password", key="login_password")
        
        if st.button("Login", key="login_button"):
            if email and password:
                dental_ai = EnhancedDentalAI()
                if dental_ai.authenticate_user(email, password):
                    st.success("✅ Login successful!")
                    st.session_state.logged_in = True
                    st.rerun()
                else:
                    st.error("❌ Invalid credentials")
                dental_ai.close()
            else:
                st.error("Please enter both email and password")
    
    with tab2:
        st.subheader("Create New Account")
        name = st.text_input("Full Name", key="register_name")
        email = st.text_input("Email", key="register_email")
        password = st.text_input("Password", type="password", key="register_password")
        role = st.selectbox("Role", ["user", "dentist"], key="register_role")
        
        if st.button("Register", key="register_button"):
            if name and email and password:
                dental_ai = EnhancedDentalAI()
                if dental_ai.create_user(name, email, password, role):
                    st.success("✅ Account created successfully! Please login.")
                else:
                    st.error("❌ Failed to create account")
                dental_ai.close()
            else:
                st.error("Please fill in all fields")

def main():
    """Main application"""
    st.set_page_config(
        page_title="Enhanced Dental AI",
        page_icon="🦷",
        layout="wide"
    )
    
    # Initialize session state
    if 'logged_in' not in st.session_state:
        st.session_state.logged_in = False
    if 'current_user' not in st.session_state:
        st.session_state.current_user = None
    
    # Show login page if not logged in and database is available
    if DATABASE_AVAILABLE and not st.session_state.logged_in:
        show_login_page()
        return
    
    # Main application
    st.title("🦷 Enhanced Dental AI Assistant")
    
    if st.session_state.current_user:
        st.sidebar.write(f"👤 Welcome, {st.session_state.current_user['name']}")
        st.sidebar.write(f"📧 {st.session_state.current_user['email']}")
        st.sidebar.write(f"🏷️ Role: {st.session_state.current_user['role']}")
        
        if st.sidebar.button("Logout"):
            st.session_state.logged_in = False
            st.session_state.current_user = None
            st.rerun()
    
    # Initialize enhanced dental AI
    if 'enhanced_ai' not in st.session_state:
        st.session_state.enhanced_ai = EnhancedDentalAI()
    
    dental_ai = st.session_state.enhanced_ai
    
    # Database status
    if DATABASE_AVAILABLE:
        if dental_ai.db_manager:
            st.sidebar.success("🗄️ Database Connected")
        else:
            st.sidebar.warning("🗄️ Database Unavailable")
    else:
        st.sidebar.info("🗄️ Database Integration Disabled")
    
    # File upload section
    st.header("📚 Upload Documents")
    uploaded_files = st.file_uploader(
        "Choose your dental documents",
        type=["pdf", "docx", "pptx"],
        accept_multiple_files=True
    )
    
    if uploaded_files:
        # Track uploads in database
        for uploaded_file in uploaded_files:
            if dental_ai.current_user:
                # Create temporary file path
                temp_path = f"/tmp/{uploaded_file.name}"
                dental_ai.track_upload(uploaded_file, temp_path)
        
        # Process documents
        with st.spinner("Processing documents..."):
            success = dental_ai.process_documents_with_tracking(uploaded_files)
            
            if success:
                st.success("✅ Documents processed successfully!")
            else:
                st.error("❌ Failed to process documents")
    
    # Chat interface
    if dental_ai.pdf_assistant.has_documents or not DATABASE_AVAILABLE:
        st.header("💬 Chat with Your Documents")
        
        # Difficulty level
        difficulty = st.selectbox(
            "Explanation Level:",
            ["simple", "normal", "detailed"],
            index=1
        )
        
        # Question input
        question = st.text_input("Ask a question about your documents:")
        
        if st.button("Ask") and question:
            with st.spinner("Thinking..."):
                response = dental_ai.answer_question_with_tracking(question, difficulty)
                
                st.write("**Answer:**")
                st.write(response['answer'])
                
                if response.get('source_type') == 'PDF':
                    st.info(f"📄 Source: {response.get('method', 'Document')}")
    
    # User dashboard (if database available)
    if DATABASE_AVAILABLE and dental_ai.current_user:
        st.header("📊 Your Dashboard")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📁 Your Uploads")
            uploads = dental_ai.get_user_uploads()
            if uploads:
                for upload in uploads[:5]:  # Show last 5 uploads
                    status_icon = {"completed": "✅", "processing": "⏳", "failed": "❌", "pending": "📋"}.get(upload['status'], "❓")
                    st.write(f"{status_icon} {upload['original_filename']} ({upload['status']})")
            else:
                st.info("No uploads yet")
        
        with col2:
            st.subheader("💬 Recent Chats")
            chats = dental_ai.get_chat_history(5)
            if chats:
                for chat in chats:
                    st.write(f"**Q:** {chat['question'][:50]}...")
                    st.write(f"**A:** {chat['answer'][:100]}...")
                    st.write("---")
            else:
                st.info("No chat history yet")

if __name__ == "__main__":
    main()
