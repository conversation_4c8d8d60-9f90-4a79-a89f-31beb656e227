import streamlit as st
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv(dotenv_path='key.env')

def main():
    st.set_page_config(
        page_title="🧠 PDF AI Assistant",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    st.title("🧠 PDF AI Assistant - Test Version")
    
    # Test API key loading
    gemini_key = os.getenv("GEMINI_API_KEY", "")
    
    if gemini_key:
        st.success(f"✅ **API Key Status:** Found (Length: {len(gemini_key)})")
    else:
        st.error("❌ **API Key Status:** Not found")
    
    # Test basic functionality
    st.header("📚 Upload Your Documents")
    uploaded_files = st.file_uploader("Choose PDF files", type="pdf", accept_multiple_files=True)
    
    if uploaded_files:
        st.success(f"✅ Successfully uploaded {len(uploaded_files)} files")
    
    # Test question input
    st.header("💬 Ask Your Question")
    question = st.text_input("Your question:", placeholder="Ask anything...")
    
    if st.button("🚀 Get Answer") and question:
        st.write(f"**You asked:** {question}")
        st.write("**Answer:** This is a test response to verify the app is working!")
    
    # Test session state
    if 'test_counter' not in st.session_state:
        st.session_state.test_counter = 0
    
    if st.button("Test Counter"):
        st.session_state.test_counter += 1
    
    st.write(f"**Counter:** {st.session_state.test_counter}")

if __name__ == "__main__":
    main()
