#!/usr/bin/env python3
"""
Complete PostgreSQL Database Setup for ADDSS
Creates all tables with full functionality
"""

import psycopg2
import bcrypt
import json
from datetime import datetime

def create_complete_database():
    """Create complete database schema with all ADDSS features"""
    
    # Connect to dental_ai database
    conn = psycopg2.connect(
        host='localhost', 
        port='5432', 
        user='postgres', 
        password='durga1976', 
        database='dental_ai'
    )
    conn.autocommit = True
    cur = conn.cursor()
    
    print("🔧 Creating complete ADDSS database schema...")
    
    # 1. Users table for authentication and user management
    cur.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'dentist', 'student')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP,
        profile_data JSONB DEFAULT '{}'
    );
    ''')
    print("✅ Users table created")
    
    # 2. Uploads table for tracking document uploads
    cur.execute('''
    CREATE TABLE IF NOT EXISTS uploads (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        filename TEXT NOT NULL,
        original_filename TEXT NOT NULL,
        file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'docx', 'pptx', 'jpg', 'png', 'dicom')),
        file_size INTEGER,
        file_path TEXT,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
        processing_info JSONB DEFAULT '{}',
        extracted_text TEXT,
        chunk_count INTEGER DEFAULT 0,
        entity_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ''')
    print("✅ Uploads table created")
    
    # 3. Chat history for conversation tracking
    cur.execute('''
    CREATE TABLE IF NOT EXISTS chat_history (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        upload_id INTEGER REFERENCES uploads(id) ON DELETE SET NULL,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        context_type TEXT DEFAULT 'general' CHECK (context_type IN ('general', 'document', 'knowledge_graph', 'rag')),
        difficulty_level TEXT DEFAULT 'normal' CHECK (difficulty_level IN ('simple', 'normal', 'detailed')),
        response_time_ms INTEGER,
        tokens_used INTEGER,
        model_used TEXT,
        knowledge_graph_used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ''')
    print("✅ Chat history table created")
    
    # 4. Knowledge graph entities (PostgreSQL backup/search)
    cur.execute('''
    CREATE TABLE IF NOT EXISTS kg_entities (
        id SERIAL PRIMARY KEY,
        upload_id INTEGER REFERENCES uploads(id) ON DELETE CASCADE,
        entity_text TEXT NOT NULL,
        entity_type TEXT NOT NULL CHECK (entity_type IN ('DISEASE', 'SYMPTOM', 'PROCEDURE', 'MATERIAL', 'INSTRUMENT', 'ANATOMICAL_STRUCTURE')),
        confidence REAL DEFAULT 1.0,
        start_pos INTEGER,
        end_pos INTEGER,
        neo4j_id TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ''')
    print("✅ Knowledge graph entities table created")
    
    # 5. Knowledge graph relations (PostgreSQL backup/search)
    cur.execute('''
    CREATE TABLE IF NOT EXISTS kg_relations (
        id SERIAL PRIMARY KEY,
        upload_id INTEGER REFERENCES uploads(id) ON DELETE CASCADE,
        head_entity TEXT NOT NULL,
        tail_entity TEXT NOT NULL,
        relation_type TEXT NOT NULL CHECK (relation_type IN ('causes', 'treats', 'indicates', 'uses_material', 'part_of', 'has_symptom')),
        confidence REAL DEFAULT 1.0,
        source_text TEXT,
        neo4j_id TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ''')
    print("✅ Knowledge graph relations table created")
    
    # 6. Document chunks for RAG (using TEXT for embeddings)
    cur.execute('''
    CREATE TABLE IF NOT EXISTS document_chunks (
        id SERIAL PRIMARY KEY,
        upload_id INTEGER REFERENCES uploads(id) ON DELETE CASCADE,
        chunk_text TEXT NOT NULL,
        chunk_index INTEGER NOT NULL,
        embedding_text TEXT, -- JSON string of embedding vector
        similarity_score REAL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ''')
    print("✅ Document chunks table created")
    
    # 7. User sessions for authentication
    cur.execute('''
    CREATE TABLE IF NOT EXISTS user_sessions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        session_token TEXT UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ''')
    print("✅ User sessions table created")
    
    # 8. System analytics
    cur.execute('''
    CREATE TABLE IF NOT EXISTS system_analytics (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
        action_type TEXT NOT NULL CHECK (action_type IN ('login', 'upload', 'query', 'download', 'error')),
        action_data JSONB DEFAULT '{}',
        ip_address TEXT,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ''')
    print("✅ System analytics table created")
    
    return cur, conn

def create_indexes(cur):
    """Create performance indexes"""
    print("🔧 Creating performance indexes...")
    
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);",
        "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);",
        "CREATE INDEX IF NOT EXISTS idx_uploads_user_id ON uploads(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_uploads_status ON uploads(status);",
        "CREATE INDEX IF NOT EXISTS idx_uploads_file_type ON uploads(file_type);",
        "CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_chat_history_upload_id ON chat_history(upload_id);",
        "CREATE INDEX IF NOT EXISTS idx_chat_history_created_at ON chat_history(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_kg_entities_upload_id ON kg_entities(upload_id);",
        "CREATE INDEX IF NOT EXISTS idx_kg_entities_type ON kg_entities(entity_type);",
        "CREATE INDEX IF NOT EXISTS idx_kg_entities_text ON kg_entities(entity_text);",
        "CREATE INDEX IF NOT EXISTS idx_kg_relations_upload_id ON kg_relations(upload_id);",
        "CREATE INDEX IF NOT EXISTS idx_kg_relations_type ON kg_relations(relation_type);",
        "CREATE INDEX IF NOT EXISTS idx_kg_relations_entities ON kg_relations(head_entity, tail_entity);",
        "CREATE INDEX IF NOT EXISTS idx_document_chunks_upload_id ON document_chunks(upload_id);",
        "CREATE INDEX IF NOT EXISTS idx_document_chunks_index ON document_chunks(chunk_index);",
        "CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);",
        "CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_system_analytics_user_id ON system_analytics(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_system_analytics_action ON system_analytics(action_type);",
        "CREATE INDEX IF NOT EXISTS idx_system_analytics_created_at ON system_analytics(created_at);"
    ]
    
    for index_sql in indexes:
        cur.execute(index_sql)
    
    print("✅ All indexes created")

def create_triggers(cur):
    """Create database triggers"""
    print("🔧 Creating database triggers...")
    
    # Updated_at trigger function
    cur.execute('''
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    ''')
    
    # Apply triggers
    triggers = [
        "CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();",
        "CREATE TRIGGER update_uploads_updated_at BEFORE UPDATE ON uploads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();"
    ]
    
    for trigger_sql in triggers:
        try:
            cur.execute(trigger_sql)
        except psycopg2.errors.DuplicateObject:
            pass  # Trigger already exists
    
    print("✅ Database triggers created")

def create_sample_users(cur):
    """Create sample users for testing"""
    print("🔧 Creating sample users...")
    
    # Hash passwords
    admin_password = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    dentist_password = bcrypt.hashpw('dentist123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    student_password = bcrypt.hashpw('student123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    users = [
        ('Admin User', '<EMAIL>', admin_password, 'admin'),
        ('Dr. Smith', '<EMAIL>', dentist_password, 'dentist'),
        ('Student User', '<EMAIL>', student_password, 'student'),
        ('Test User', '<EMAIL>', student_password, 'user')
    ]
    
    for name, email, password_hash, role in users:
        try:
            cur.execute('''
            INSERT INTO users (name, email, password_hash, role) 
            VALUES (%s, %s, %s, %s)
            ON CONFLICT (email) DO NOTHING;
            ''', (name, email, password_hash, role))
        except Exception as e:
            print(f"⚠️ User {email} already exists or error: {e}")
    
    print("✅ Sample users created")
    print("📝 Login credentials:")
    print("   Admin: <EMAIL> / admin123")
    print("   Dentist: <EMAIL> / dentist123") 
    print("   Student: <EMAIL> / student123")
    print("   User: <EMAIL> / student123")

def verify_database(cur):
    """Verify database setup"""
    print("🔧 Verifying database setup...")
    
    # Check tables
    cur.execute("""
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    ORDER BY table_name;
    """)
    
    tables = [row[0] for row in cur.fetchall()]
    expected_tables = ['users', 'uploads', 'chat_history', 'kg_entities', 'kg_relations', 'document_chunks', 'user_sessions', 'system_analytics']
    
    print(f"📊 Tables created: {len(tables)}")
    for table in tables:
        print(f"   ✅ {table}")
    
    # Check users
    cur.execute("SELECT COUNT(*) FROM users;")
    user_count = cur.fetchone()[0]
    print(f"👥 Users created: {user_count}")
    
    return len(tables) >= len(expected_tables)

if __name__ == "__main__":
    try:
        cur, conn = create_complete_database()
        create_indexes(cur)
        create_triggers(cur)
        create_sample_users(cur)
        
        if verify_database(cur):
            print("\n🎉 COMPLETE ADDSS DATABASE SETUP SUCCESSFUL!")
            print("🚀 All functionalities are now available:")
            print("   ✅ User authentication and management")
            print("   ✅ Upload tracking and status")
            print("   ✅ Persistent chat history")
            print("   ✅ Document chunk storage for RAG")
            print("   ✅ Knowledge graph backup in PostgreSQL")
            print("   ✅ User analytics and dashboard")
            print("   ✅ Session management")
            print("   ✅ Performance optimization")
        else:
            print("❌ Database setup incomplete")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        import traceback
        traceback.print_exc()
