#!/usr/bin/env python3
"""
PDF Processing Microservice
Extracted from dental_ai_enhanced.py for containerized deployment
"""

from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import tempfile
import os
from typing import List, Dict, Any
import logging

# Import your existing PDF processing logic
from fixed_ai_assistant import PDFAssistant

app = FastAPI(title="ADDSS PDF Processing Service", version="1.0.0")
logger = logging.getLogger(__name__)

class PDFProcessingService:
    def __init__(self):
        self.pdf_assistant = PDFAssistant()
    
    async def process_pdf(self, file: UploadFile) -> Dict[str, Any]:
        """Process uploaded PDF file"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
                content = await file.read()
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # Process with existing PDF assistant
            success = self.pdf_assistant.add_documents([file])
            
            if success:
                extracted_content = self.pdf_assistant.documents_content
                
                # Clean up
                os.unlink(temp_file_path)
                
                return {
                    "status": "success",
                    "filename": file.filename,
                    "content_length": len(extracted_content),
                    "extracted_text": extracted_content[:1000] + "..." if len(extracted_content) > 1000 else extracted_content
                }
            else:
                raise HTTPException(status_code=400, detail="Failed to process PDF")
                
        except Exception as e:
            logger.error(f"PDF processing error: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# Initialize service
pdf_service = PDFProcessingService()

@app.post("/process-pdf")
async def process_pdf_endpoint(file: UploadFile = File(...)):
    """Process PDF file and extract content"""
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")
    
    result = await pdf_service.process_pdf(file)
    return JSONResponse(content=result)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "pdf-processing"}

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "ADDSS PDF Processing Service", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
