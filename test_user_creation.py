#!/usr/bin/env python3
"""Test user creation with detailed error handling"""

import psycopg2
import bcrypt
from database_manager import DatabaseManager

def test_direct_user_creation():
    """Test user creation directly with PostgreSQL"""
    
    try:
        # Direct PostgreSQL connection
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            user='postgres',
            password='durga1976',
            database='dental_ai'
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        # Create user directly
        name = 'Direct Test User'
        email = '<EMAIL>'
        password = 'directpass123'
        role = 'dentist'
        
        # Hash password
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # Insert user
        query = """
            INSERT INTO users (name, email, password_hash, role, is_active)
            VALUES (%s, %s, %s, %s, %s)
            RETURNING id
        """
        
        cur.execute(query, (name, email, password_hash, role, True))
        result = cur.fetchone()
        
        if result:
            user_id = result[0]
            print(f"✅ User created directly with ID: {user_id}")
            
            # Test authentication
            auth_query = "SELECT id, name, email, password_hash, role FROM users WHERE email = %s AND is_active = TRUE"
            cur.execute(auth_query, (email,))
            auth_result = cur.fetchone()
            
            if auth_result:
                stored_hash = auth_result[3]  # password_hash column
                is_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
                print(f"✅ Authentication test: {is_valid}")
                
                if is_valid:
                    print(f"✅ User {email} created and authenticated successfully!")
                    return email, password
            else:
                print("❌ User not found for authentication")
        else:
            print("❌ User creation failed - no ID returned")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ Direct creation error: {e}")
        import traceback
        traceback.print_exc()
        
    return None, None

def test_database_manager_creation():
    """Test user creation through DatabaseManager"""
    
    try:
        db = DatabaseManager()
        
        # Test with unique email
        name = 'Manager Test User'
        email = '<EMAIL>'
        password = 'managerpass123'
        role = 'dentist'
        
        print(f"Creating user: {name} ({email}) with role: {role}")
        
        user_id = db.postgres.create_user(name, email, password, role)
        
        if user_id:
            print(f"✅ User created through manager with ID: {user_id}")
            
            # Test authentication
            user = db.postgres.authenticate_user(email, password)
            if user:
                print(f"✅ Authentication successful: {user}")
                return email, password
            else:
                print("❌ Authentication failed")
        else:
            print("❌ User creation through manager failed")
            
    except Exception as e:
        print(f"❌ Manager creation error: {e}")
        import traceback
        traceback.print_exc()
        
    return None, None

if __name__ == "__main__":
    print("🧪 Testing User Creation Methods")
    print("=" * 50)
    
    print("\n1. Testing direct PostgreSQL creation:")
    direct_email, direct_pass = test_direct_user_creation()
    
    print("\n2. Testing DatabaseManager creation:")
    manager_email, manager_pass = test_database_manager_creation()
    
    print("\n📋 Summary:")
    if direct_email:
        print(f"✅ Direct method: {direct_email} / {direct_pass}")
    if manager_email:
        print(f"✅ Manager method: {manager_email} / {manager_pass}")
        
    if direct_email or manager_email:
        print("\n🎉 At least one method works! You can create accounts.")
    else:
        print("\n❌ Both methods failed. Need to investigate further.")
