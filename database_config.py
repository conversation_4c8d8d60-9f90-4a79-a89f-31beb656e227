#!/usr/bin/env python3
"""
Database Configuration for Dental AI System
Update these settings to match your PostgreSQL and Neo4j installations
"""

import os

class DatabaseConfig:
    """Database configuration settings"""
    
    # PostgreSQL settings - Update these for your installation
    POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
    POSTGRES_PORT = os.getenv('POSTGRES_PORT', '5432')
    POSTGRES_DB = os.getenv('POSTGRES_DB', 'dental_ai')
    POSTGRES_USER = os.getenv('POSTGRES_USER', 'postgres')
    POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'your_postgres_password')  # CHANGE THIS!
    
    # Neo4j settings - Update these for your installation
    NEO4J_URI = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    NEO4J_USER = os.getenv('NEO4J_USER', 'neo4j')
    NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD', 'durga1976')  # CHANGE THIS!
    
    @classmethod
    def get_postgres_url(cls):
        return f"postgresql://{cls.POSTGRES_USER}:{cls.POSTGRES_PASSWORD}@{cls.POSTGRES_HOST}:{cls.POSTGRES_PORT}/{cls.POSTGRES_DB}"
    
    @classmethod
    def print_config(cls):
        """Print current configuration (without passwords)"""
        print("🔧 Database Configuration:")
        print(f"  PostgreSQL: {cls.POSTGRES_HOST}:{cls.POSTGRES_PORT}/{cls.POSTGRES_DB}")
        print(f"  Neo4j: {cls.NEO4J_URI}")
        print(f"  Users: postgres={cls.POSTGRES_USER}, neo4j={cls.NEO4J_USER}")

# Instructions for setup
SETUP_INSTRUCTIONS = """
🚀 Database Setup Instructions:

1. 📦 PostgreSQL Setup:
   - Make sure PostgreSQL is running on localhost:5432
   - Create database: CREATE DATABASE dental_ai;
   - Update POSTGRES_PASSWORD in this file
   - Run: python -c "from database_manager import test_connections; test_connections()"

2. 🕸️ Neo4j Setup:
   - Make sure Neo4j is running on localhost:7687
   - Open Neo4j Browser: http://localhost:7474
   - Update NEO4J_PASSWORD in this file
   - Run the commands in neo4j_setup.cypher

3. 🧪 Test Connections:
   - Run: python database_manager.py
   - Should see: "✅ PostgreSQL connected" and "✅ Neo4j connected"

4. 📊 Create Tables:
   - Run the SQL commands in database_setup.sql in your PostgreSQL database
   - Use pgAdmin or psql command line

5. 🎯 Ready to Use:
   - Your dental AI system can now use both databases!
"""

if __name__ == "__main__":
    DatabaseConfig.print_config()
    print(SETUP_INSTRUCTIONS)
