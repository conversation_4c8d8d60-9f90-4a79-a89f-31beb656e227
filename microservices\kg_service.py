#!/usr/bin/env python3
"""
Knowledge Graph Microservice
Extracted from dental_ner_kg.py for containerized deployment
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any
import logging

# Import your existing KG logic
from dental_ner_kg import build_dental_knowledge_graph, DentalKnowledgeGraph

app = FastAPI(title="ADDSS Knowledge Graph Service", version="1.0.0")
logger = logging.getLogger(__name__)

class TextInput(BaseModel):
    text: str
    upload_id: int = None

class EntityQuery(BaseModel):
    entity_name: str
    max_results: int = 10

class KGService:
    def __init__(self):
        self.kg_cache = {}  # Simple in-memory cache
    
    def build_knowledge_graph(self, text: str, upload_id: int = None) -> Dict[str, Any]:
        """Build knowledge graph from text"""
        try:
            # Use existing KG builder
            kg = build_dental_knowledge_graph(text)
            
            # Cache the result
            if upload_id:
                self.kg_cache[upload_id] = kg
            
            # Return statistics and entities
            stats = kg.get_stats()
            entities = kg.get_entities()
            relations = kg.get_relations()
            
            return {
                "status": "success",
                "upload_id": upload_id,
                "statistics": stats,
                "entities": entities[:50],  # Limit for API response
                "relations": relations[:50],  # Limit for API response
                "total_entities": len(entities),
                "total_relations": len(relations)
            }
            
        except Exception as e:
            logger.error(f"KG construction error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def query_entities(self, entity_name: str, max_results: int = 10) -> List[Dict]:
        """Query related entities"""
        try:
            # Search across all cached KGs
            results = []
            for upload_id, kg in self.kg_cache.items():
                related = kg.get_related_entities(entity_name)
                for entity in related[:max_results]:
                    results.append({
                        "entity": entity,
                        "upload_id": upload_id,
                        "relevance_score": 1.0  # Placeholder
                    })
            
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"Entity query error: {e}")
            raise HTTPException(status_code=500, detail=str(e))

# Initialize service
kg_service = KGService()

@app.post("/build-kg")
async def build_kg_endpoint(input_data: TextInput):
    """Build knowledge graph from text"""
    result = kg_service.build_knowledge_graph(input_data.text, input_data.upload_id)
    return result

@app.post("/query-entities")
async def query_entities_endpoint(query: EntityQuery):
    """Query related entities"""
    results = kg_service.query_entities(query.entity_name, query.max_results)
    return {"entities": results}

@app.get("/stats/{upload_id}")
async def get_kg_stats(upload_id: int):
    """Get knowledge graph statistics"""
    if upload_id not in kg_service.kg_cache:
        raise HTTPException(status_code=404, detail="Knowledge graph not found")
    
    kg = kg_service.kg_cache[upload_id]
    stats = kg.get_stats()
    return stats

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "knowledge-graph"}

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "ADDSS Knowledge Graph Service", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
