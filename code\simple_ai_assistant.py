import os
import streamlit as st
import google.generativeai as genai
from dotenv import load_dotenv
import time

# Load API keys
load_dotenv(dotenv_path='key.env')
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")

# Configure API
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)

class SimpleAIAssistant:
    def __init__(self):
        self.has_documents = False
        self.documents_content = ""
    
    def add_documents(self, pdf_files):
        """Simple document processing"""
        try:
            import fitz  # PyMuPDF
            all_text = ""
            
            for pdf_file in pdf_files:
                # Save file temporarily
                temp_path = f"temp_{pdf_file.name}"
                with open(temp_path, "wb") as f:
                    f.write(pdf_file.getbuffer())
                
                # Extract text
                doc = fitz.open(temp_path)
                text = ""
                for page in doc:
                    text += page.get_text() + "\n"
                doc.close()
                
                all_text += f"\n\n=== From {pdf_file.name} ===\n{text}"
                
                # Clean up
                os.remove(temp_path)
            
            if all_text:
                self.documents_content = all_text
                self.has_documents = True
                return True
            return False
        except Exception as e:
            st.error(f"Error processing PDFs: {str(e)}")
            return False
    
    def answer_question(self, question, conversation_history=None, difficulty_level="normal"):
        """Answer questions with conversation context"""
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            # Build conversation context
            context_prompt = ""
            if conversation_history:
                recent_context = conversation_history[-3:]
                context_prompt = "\n\nPREVIOUS CONVERSATION:\n"
                for i, chat in enumerate(recent_context):
                    context_prompt += f"Q{i+1}: {chat['question']}\nA{i+1}: {chat['answer'][:200]}...\n"
            
            # Check if summarization request
            if any(keyword in question.lower() for keyword in ["summarize", "summary", "overview", "key points"]):
                if self.has_documents:
                    prompt = f"""You are an expert academic tutor. Create a comprehensive summary of the following content:

{context_prompt}

STUDENT'S DOCUMENTS:
{self.documents_content[:8000]}

SUMMARIZATION REQUEST: {question}

Create a well-structured summary with:
1. Main Topics/Concepts
2. Important Definitions  
3. Key Points
4. Examples
5. Exam Focus Points

Format with clear headings and bullet points."""
                    
                    response = model.generate_content(prompt)
                    return {
                        'answer': response.text,
                        'source_type': 'PDF',
                        'method': 'PDF Summarization',
                        'is_summary': True
                    }
                else:
                    return {
                        'answer': "I don't have any documents uploaded to summarize. Please upload your PDFs first.",
                        'source_type': 'AI',
                        'method': 'No Documents',
                        'is_summary': True
                    }
            
            # Regular question handling
            if self.has_documents:
                # Simple keyword search in documents
                question_words = question.lower().split()
                relevant_content = ""
                
                for word in question_words:
                    if word in self.documents_content.lower():
                        # Find context around the word
                        start = max(0, self.documents_content.lower().find(word) - 500)
                        end = min(len(self.documents_content), start + 1000)
                        relevant_content = self.documents_content[start:end]
                        break
                
                if relevant_content:
                    prompt = f"""You are a friendly AI tutor. Answer based on the document content and conversation history.

{context_prompt}

DOCUMENT CONTENT:
{relevant_content}

Question: {question}

Provide a helpful, educational answer based on the document content."""
                    
                    response = model.generate_content(prompt)
                    return {
                        'answer': response.text,
                        'source_type': 'PDF',
                        'method': 'Document Search'
                    }
            
            # AI knowledge fallback
            prompt = f"""You are a friendly AI tutor helping with exam preparation.

{context_prompt}

Question: {question}

Provide a comprehensive, educational answer with examples and clear explanations."""
            
            response = model.generate_content(prompt)
            return {
                'answer': response.text,
                'source_type': 'AI',
                'method': 'AI Knowledge'
            }
            
        except Exception as e:
            return {
                'answer': f"I encountered an error: {str(e)}. Please try again.",
                'source_type': 'Error',
                'method': 'Error Handler'
            }

def main():
    st.set_page_config(
        page_title="🧠 Simple AI Assistant",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    st.title("🧠 Simple AI Assistant")
    st.markdown("**Upload PDFs and chat with your AI tutor!**")
    
    # Check API key
    if not GEMINI_API_KEY:
        st.error("❌ **GEMINI_API_KEY not found!** Please add your API key to continue.")
        st.stop()
    
    st.success("✅ **System Status:** Ready to help!")
    
    # Initialize
    if 'assistant' not in st.session_state:
        st.session_state.assistant = SimpleAIAssistant()
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    # Document upload
    st.header("📚 Upload Your Documents")
    uploaded_files = st.file_uploader("Choose PDF files", type="pdf", accept_multiple_files=True)
    
    if uploaded_files:
        with st.spinner("Processing PDFs..."):
            success = st.session_state.assistant.add_documents(uploaded_files)
            if success:
                st.success(f"✅ Successfully processed {len(uploaded_files)} PDFs")
            else:
                st.error("❌ Could not process PDFs")

    # Chat interface
    st.header("💬 Chat with Your AI Tutor")

    # Quick buttons
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("🤖 What is machine learning?"):
            st.session_state.current_q = "What is machine learning?"
    with col2:
        if st.button("📋 Summarize my PDFs"):
            st.session_state.current_q = "Please provide a comprehensive summary of my uploaded documents"
    with col3:
        if st.button("🎯 Key points"):
            st.session_state.current_q = "Give me the key points from my documents"

    # Question input
    question = st.text_area(
        "Type your message:",
        value=st.session_state.get('current_q', ''),
        placeholder="💬 Ask questions, request summaries, or chat about any topic...",
        height=100
    )

    if 'current_q' in st.session_state:
        del st.session_state.current_q

    # Get answer
    if st.button("🚀 Get Answer", type="primary") and question:
        with st.spinner("🔍 Thinking..."):
            response = st.session_state.assistant.answer_question(
                question,
                conversation_history=st.session_state.chat_history
            )

            # Display response
            if response.get('source_type') == 'PDF':
                st.success("✅ **Answer from your PDFs**")
            else:
                st.info("🤖 **Answer from AI knowledge**")

            st.write("### 🎯 Response")
            st.write(response['answer'])

            # Add to history
            st.session_state.chat_history.append({
                'question': question,
                'answer': response['answer'],
                'source_type': response.get('source_type', 'AI'),
                'method': response.get('method', 'Unknown'),
                'timestamp': time.time()
            })

    # Chat history
    if st.session_state.chat_history:
        st.header("💬 Chat History")
        for i, chat in enumerate(st.session_state.chat_history[-3:]):
            with st.expander(f"Q{i+1}: {chat['question'][:50]}..."):
                st.write(f"**You:** {chat['question']}")
                st.write(f"**AI:** {chat['answer'][:200]}...")
                st.caption(f"Source: {chat.get('source_type', 'AI')} | Method: {chat.get('method', 'Unknown')}")

if __name__ == "__main__":
    main()
