Para 1: Product Requirements Document: AI-Powered Dental Decision Support System (MVP)
Para 2: Version: 1.0 Date: 2025-12-18 Status: Draft
Para 3: 1. Introduction
Para 4: 1.1 Purpose This document defines the Product Requirements (PRD) for the Minimum Viable Product (MVP) of the AI-Powered Dental Decision Support System (ADDSS). The primary objective of this MVP is to validate the core technical feasibility and demonstrate the potential value of extracting, structuring, and querying knowledge derived from dental textbooks. This will be achieved through an AI-driven chat interface that incorporates contextual image uploads and offers flexibility in the underlying Large Language Model (LLM) configuration. This PRD serves as the definitive specification for the features, functionalities, and constraints of the MVP.
Para 5: 1.2 System Overview The ADDSS MVP is designed as a foundational system that ingests dental textbook content in PDF format. It will employ advanced document processing techniques, including Optical Character Recognition (OCR) and layout analysis, to accurately extract textual information. This extracted text will serve as the basis for constructing a domain-specific Knowledge Graph (KG), capturing key dental entities (like diseases, symptoms, procedures) and their interrelationships.The core user interaction mechanism will be a web-based chat interface. Users can pose natural language questions related to dental knowledge contained within the ingested textbooks. The system will utilize Retrieval-Augmented Generation (RAG) techniques, querying the KG and source text corpus to retrieve relevant context before generating responses with an LLM. A key feature of the MVP is the ability for users to upload relevant image files (e.g., X-rays, clinical photos in JPG/PNG format, potentially basic DICOM wrappers) within the chat, providing visual context for their queries, although the MVP will not perform AI analysis on these images.Furthermore, the system architecture will support flexible LLM configuration. While defaulting to Google Cloud's Vertex AI Gemini models for response generation , the backend will allow users to configure and utilize external LLM providers, such as those accessible via OpenRouter, by providing an API endpoint and key.
Para 6: 1.3 Target Audience for this Document This PRD is intended for internal stakeholders involved in the planning, development, testing, and management of the ADDSS MVP. This includes, but is not limited to:
Para 7: Product Owners
Para 8: Engineering Leads (Backend, Frontend, AI/ML)
Para 9: Software Developers
Para 10: AI/ML Engineers
Para 11: Quality Assurance (QA) Engineers
Para 12: Project Managers
Para 13: 1.4 Document Conventions
Para 14: Requirements specified in this document are uniquely identified using prefixes:
Para 15: OBJ-XXX: Objective
Para 16: FR-PDF-XXX: Functional Requirement - PDF Processing
Para 17: FR-KG-XXX: Functional Requirement - Knowledge Graph Construction
Para 18: FR-API-XXX: Functional Requirement - Backend API
Para 19: FR-UI-XXX: Functional Requirement - Chat User Interface
Para 20: FR-LLM-XXX: Functional Requirement - LLM Configuration
Para 21: NFR-ACC-XXX: Non-Functional Requirement - Accuracy
Para 22: NFR-PRF-XXX: Non-Functional Requirement - Performance
Para 23: NFR-SEC-XXX: Non-Functional Requirement - Security
Para 24: DATA-XXX: Data Requirement
Para 25: TECH-XXX: Technology Stack Recommendation
Para 26: References supporting specific requirements or providing context are indicated in square brackets, citing the relevant source ID (e.g.).
Para 27: 2. MVP Scope and Objectives
Para 28: 2.1 Objectives The ADDSS MVP aims to achieve the following primary objectives:
Para 29: OBJ-001: Demonstrate the core capability of ingesting and processing dental textbook PDFs to accurately extract textual content and essential layout information. This forms the foundation for subsequent knowledge extraction.
Para 30: OBJ-002: Prove the feasibility of constructing a foundational dental Knowledge Graph (KG) from the extracted textbook content. This involves identifying key entities (e.g., diseases, symptoms, procedures) and their semantic relationships (e.g., causes, treats, indicates) using NLP techniques.
Para 31: OBJ-003: Implement a functional chat interface enabling users to query the constructed KG and source text using natural language. This interface will leverage Retrieval-Augmented Generation (RAG) to ensure responses are grounded in the ingested textbook knowledge, enhancing factual accuracy.
Para 32: OBJ-004: Enable users to upload single or multiple image files (common formats like JPG, PNG, and potentially basic DICOM wrappers) associated with specific chat messages. This provides necessary visual context for queries, laying the groundwork for future multimodal analysis.
Para 33: OBJ-005: Provide backend flexibility for configuring and selecting Large Language Models (LLMs). The system will default to Google Cloud Vertex AI (specifically, Gemini models) but allow users to specify alternative external providers (e.g., via OpenRouter) by configuring an API endpoint URL and API Key.
Para 34: 2.2 Out of Scope for MVP To maintain focus and ensure timely delivery of core functionality, the following capabilities are explicitly excluded from the MVP scope:
Para 35: AI-based Image Analysis: The system will accept image uploads for context but will not perform any automated analysis or interpretation of the image content (e.g., identifying pathologies, segmenting structures). This is a planned future enhancement.
Para 36: Advanced KG Reasoning: Complex reasoning tasks beyond RAG-based question answering, such as multi-hop inference for differential diagnosis or treatment plan generation, are out of scope.
Para 37: User Authentication & Management: Comprehensive user account management, roles, and permissions are deferred. Basic API key handling for external LLMs is included.
Para 38: EHR/PMS Integration: No integration with external Electronic Health Records or Practice Management Systems will be implemented in the MVP.
Para 39: Complex PDF Features: Handling advanced PDF features like interactive forms, embedded multimedia, or highly complex, non-standard layouts is not prioritized.
Para 40: Advanced UI Features: Features like persistent chat history across sessions (beyond browser session storage), user profiles, sophisticated data visualizations, or administrative dashboards are excluded.
Para 41: Explainable AI (XAI): Visualization or explanation of the AI's reasoning process (e.g., highlighting contributing text passages, SHAP/LIME values) is not included in the MVP, though crucial for future clinical trust.
Para 42: Voice User Interface (VUI): Speech input or output capabilities are not part of the MVP.
Para 43: Formal Regulatory Compliance: While basic data security and privacy practices will be followed, the MVP will not undergo formal certification or validation against specific medical device regulations (e.g., EU MDR, FDA requirements, CDSCO guidelines). The focus is on technical feasibility demonstration.
Para 44: 2.3 Foundational Approach The development of AI-driven systems, particularly in knowledge-intensive domains like healthcare, necessitates a robust approach to knowledge extraction and representation. Research indicates that constructing knowledge graphs from unstructured text sources, such as medical literature or textbooks, is a viable but intricate process requiring sophisticated NLP techniques like Named Entity Recognition (NER) and Relation Extraction (RE). These KGs provide a structured foundation for information retrieval and reasoning.Large Language Models (LLMs), while powerful, can sometimes generate factually incorrect or "hallucinated" responses, or lack the specific, detailed knowledge contained within specialized corpora like dental textbooks. Retrieval-Augmented Generation (RAG) directly addresses this limitation by first retrieving relevant information from a trusted knowledge source (in this case, the extracted textbook content and the derived KG) before prompting the LLM to generate an answer. This grounding in source material significantly improves the reliability and factual accuracy of the generated responses. Integrating KGs specifically within the RAG process (KG-RAG) offers the potential for more precise context retrieval due to the structured nature of the knowledge.Furthermore, clinical decision-making in dentistry often involves visual information. While the MVP will not perform AI analysis on images, enabling users to upload images (X-rays, photos) provides essential context for their text-based queries. This necessitates a user interface capable of handling image uploads and a backend architecture that can associate this visual context with the query, influencing the information passed to the LLM and paving the way for future multimodal capabilities.Consequently, the MVP scope logically prioritizes the establishment of a reliable pipeline: PDF ingestion and processing, KG construction from the extracted text, implementation of a KG-RAG based chat interface, inclusion of image upload for context, and a flexible LLM backend architecture. This staged approach validates the core technologies and provides a foundation for subsequent enhancements, such as image analysis and deeper reasoning, post-MVP. The flexible LLM configuration is particularly important, allowing adaptation to newer, potentially multimodal models without requiring major backend refactoring later.
Para 45: 3. Functional Requirements
Para 46: 3.1 PDF Processing (FR-PDF-XXX) The system must effectively process uploaded dental textbook PDFs to extract the necessary information for KG construction and RAG. PDF parsing presents significant challenges due to the variability in document structures, including scanned versus digital text, complex layouts, and embedded elements like tables and figures. High-quality extraction is paramount, as the performance of downstream AI components depends heavily on it.
Para 47: FR-PDF-001: The system shall provide an interface (e.g., a dedicated admin UI page or an API endpoint) allowing authorized personnel to upload dental textbook PDF files for ingestion into the system.
Para 48: FR-PDF-002: The system must implement robust text extraction from uploaded PDFs. This includes accurately capturing standard text blocks, headers, footers, and image captions. For PDFs containing scanned pages or image-based text, Optical Character Recognition (OCR) technology (e.g., Tesseract) must be employed to convert images to machine-readable text.
Para 49: FR-PDF-003: Basic document layout information (e.g., paragraph boundaries, list structures, section headings, relative positioning of text blocks) must be preserved or inferred during extraction. This contextual information is vital for accurate KG construction (especially relation extraction) and for providing meaningful context snippets in the RAG process. Techniques like Document Layout Analysis should be investigated and applied where feasible.
Para 50: FR-PDF-004: The system must correctly handle multi-column text layouts, common in academic publications, ensuring text is extracted in the logical reading order.
Para 51: FR-PDF-005: The system must include functionality to detect and extract data from moderately complex tables within the PDFs. Extracted table data should be converted into a structured format (e.g., JSON array of arrays, or CSV representation) and associated with the surrounding text or table caption for context. The MVP will focus on tables with clear row/column structures; performance on highly nested, borderless, or unusually formatted tables may be limited.
Para 52: FR-PDF-006: The system should attempt to extract standard PDF metadata if available (e.g., Document Title, Author, Subject, Keywords, Creation/Modification Dates, Chapter bookmarks) and store it alongside the extracted content.
Para 53: FR-PDF-007: The PDF processing module must handle errors gracefully. This includes detecting and logging issues such as corrupted files, password-protected documents (unsupported in MVP), unsupported PDF versions, or OCR failures, without crashing the entire ingestion process.
Para 54: FR-PDF-008: The output of the PDF processing module (extracted text, layout information, table data, metadata) shall be stored in a structured, intermediate format (e.g., JSON objects per page or section, database records) suitable for consumption by the Knowledge Graph Construction module. This format should maintain links back to the source PDF and page number.
Para 55: 3.2 Knowledge Graph Construction (FR-KG-XXX) The system shall construct a foundational Knowledge Graph (KG) representing the core concepts and relationships described in the processed dental textbooks. Building KGs from text involves NER and RE, which can be achieved through traditional NLP pipelines or increasingly via LLMs. The quality and utility of the KG depend on the accuracy of these processes and a well-defined schema.
Para 56: FR-KG-001: The system shall analyze the structured text output from the PDF Processing module to identify occurrences of predefined dental domain entities.
Para 57: FR-KG-002: The system shall identify and extract predefined types of semantic relationships between the identified entities, based on the linguistic context within the source text.
Para 58: FR-KG-003: MVP Named Entity Recognition (NER) must target at least the following key dental concepts:
Para 59: Disease: Specific diseases, conditions, disorders (e.g., "Gingivitis", "Periodontitis", "Dental Caries", "Apical Lesion").
Para 60: Symptom: Observable signs or patient-reported indications (e.g., "Bleeding Gums", "Toothache", "Sensitivity to Cold").
Para 61: AnatomicalStructure: Relevant parts of the oral cavity and associated structures (e.g., "Enamel", "Dentin", "Pulp", "Mandible", "Maxillary Sinus").
Para 62: Procedure: Dental treatments, interventions, or diagnostic actions (e.g., "Root Canal Treatment", "Tooth Extraction", "Dental Filling", "Panoramic Radiograph").
Para 63: Material: Substances used in dental procedures (e.g., "Amalgam", "Composite Resin", "Gutta-percha").
Para 64: Instrument: Tools used in dentistry (e.g., "Dental Probe", "Scaler", "Forceps"). (Implementation may use libraries like spaCy/scispaCy or LLM-based extraction ).
Para 65: FR-KG-004: MVP Relation Extraction (RE) must target at least the following key relationship types:
Para 66: causes (Domain: Disease, Range: Symptom) - e.g., (Gingivitis)-[causes]->(Bleeding Gums)
Para 67: treats (Domain: Procedure, Range: Disease) - e.g., (Root Canal Treatment)-[treats]->(Apical Lesion)
Para 68: indicates (Domain: Symptom, Range: Disease) - e.g., (Toothache)-[indicates]->(Dental Caries)
Para 69: uses_material (Domain: Procedure, Range: Material) - e.g., (Dental Filling)-[uses_material]->(Composite Resin)
Para 70: part_of (Domain: AnatomicalStructure, Range: AnatomicalStructure) - e.g., (Enamel)-[part_of]->(Tooth)
Para 71: has_symptom (Domain: Disease, Range: Symptom) - e.g., (Periodontitis)-[has_symptom]->(Bone Loss) (Implementation may use zero-shot RE, rule-based methods, or LLM-based extraction ).
Para 72: FR-KG-005: The system must implement the following basic dental Knowledge Graph schema/ontology for the MVP. This schema defines the structure for storing extracted knowledge and should be designed for future extensibility. Existing dental ontologies like OHD and schema.org/Dentist serve as conceptual references but the MVP schema is simplified as follows:Table 1: MVP Dental Knowledge Graph Schema | Entity Type | Description | Attributes (Name, Type) | | -------------------- | --------------------------------------------------- | ------------------------------------ | | Disease | A specific dental disease or condition | name (string), synonyms (list[string]) | | Symptom | A sign or indication of a disease | name (string) | | AnatomicalStructure | A relevant anatomical part | name (string) | | Procedure | A dental treatment or intervention | name (string) | | Material | A substance used in dental procedures | name (string) | | Instrument | A tool used in dental procedures | name (string) | | Relationship Type| Domain (Start Entity) | Range (End Entity) | Description | | causes | Disease | Symptom | Links a disease to a symptom it causes. | | treats | Procedure | Disease | Links a procedure to a disease it treats. | | indicates | Symptom | Disease | Links a symptom to a disease it may indicate. | | uses_material | Procedure | Material | Links a procedure to a material it uses. | | part_of | AnatomicalStructure | AnatomicalStructure | Links a part to its whole structure. | | has_symptom | Disease | Symptom | Links a disease to an associated symptom. |
Para 73: FR-KG-006: The constructed KG, including nodes (entities) and edges (relationships) with their properties, shall be persisted in a suitable database system. Options include a dedicated graph database (e.g., Neo4j ) or a relational database like PostgreSQL utilizing JSONB or graph extensions, potentially combined with a vector store for embeddings. The choice should support efficient RAG retrieval patterns.
Para 74: FR-KG-007: Each extracted entity and relationship stored in the KG must maintain a reference (provenance link) back to the specific text chunk(s) and source document (PDF) from which it was derived. This is essential for grounding RAG responses and enabling potential citation features later.
Para 75: FR-KG-008: The KG construction process must implement basic normalization or entity linking where feasible for MVP (e.g., mapping common synonyms like "tooth decay" and "dental caries" to a single canonical entity). Advanced coreference resolution or complex disambiguation is out of scope.
Para 76: 3.3 Backend API (FR-API-XXX) The backend API serves as the central controller, managing user interactions, orchestrating the AI processes (RAG, LLM calls), handling file uploads, and managing configurations. It connects the UI to the underlying knowledge base and AI models.
Para 77: FR-API-001: The system shall expose its functionalities through a RESTful backend API implemented using a suitable framework (e.g., FastAPI ).
Para 78: FR-API-002: The API must provide a primary endpoint (e.g., /chat) to receive user chat messages. The request payload should include the user's query text and optionally a list of unique identifiers for any images uploaded and associated with that specific query.
Para 79: FR-API-003: Upon receiving a request at the /chat endpoint, the API shall orchestrate the Retrieval-Augmented Generation (RAG) process as follows:
Para 80: a. Parse the user's query to understand the intent and identify key entities or concepts.
Para 81: b. Query the knowledge base (which includes the KG and potentially the indexed source text chunks) to retrieve relevant context based on the query's semantics. This retrieval should leverage techniques like vector similarity search on embeddings derived from the text and/or KG elements.
Para 82: c. If image identifiers are present in the request, retrieve basic context about the images (e.g., filenames, potentially format type if stored). This image context information should be prepared for inclusion in the LLM prompt.
Para 83: d. Construct a detailed prompt for the selected LLM (determined by FR-LLM rules). This prompt must include the original user query, the retrieved textual and/or KG context, and any associated image context information. Prompt engineering techniques should be applied to guide the LLM towards generating accurate, relevant, and grounded responses.
Para 84: e. Identify and select the appropriate LLM endpoint (Default Google Vertex AI or configured external provider) based on the LLM configuration rules (FR-LLM-XXX).
Para 85: f. Send the constructed prompt to the selected LLM's API endpoint, handling authentication (API keys) as required.
Para 86: g. Receive the response generated by the LLM.
Para 87: FR-API-004: The API must return the LLM-generated response to the calling client (the chat UI). The API should support streaming responses (e.g., using Server-Sent Events or WebSockets) to the UI for improved perceived responsiveness, sending text chunks as they are generated by the LLM.
Para 88: FR-API-005: The API must provide a dedicated endpoint (e.g., /upload) for handling file uploads.
Para 89: a. The endpoint must accept POST requests containing one or more files (sent as multipart/form-data) intended to be associated with a chat message context. Supported image types are primarily JPG and PNG. If FR-UI-006 is implemented, basic DICOM wrapper upload should also be handled.
Para 90: b. Implement security checks upon upload, including enforcing maximum file size limits per file and potentially validating file MIME types against an allowed list (e.g., image/jpeg, image/png, potentially application/dicom or common image wrappers).
Para 91: c. Securely store the uploaded file(s) in a temporary, access-controlled location (e.g., a designated Google Cloud Storage bucket with appropriate permissions and lifecycle rules).
Para 92: d. Generate and return unique identifier(s) for the successfully uploaded file(s). These identifiers will be used by the UI to associate the images with the subsequent chat message (FR-API-002).
Para 93: FR-API-006: The API must implement logic to manage LLM provider configurations as defined in FR-LLM-XXX. This includes endpoints or mechanisms to:
Para 94: a. Allow storing/updating external LLM provider details (Endpoint URL, API Key, optional Preferred Model Name).
Para 95: b. Securely retrieve stored API keys when making calls to external providers, ensuring keys are never exposed in logs or client responses.
Para 96: FR-API-007: The API must contain the necessary client logic to interact with different LLM APIs (Google Cloud Vertex AI Gemini API and the generic endpoint structure for external providers like OpenRouter ). This includes:
Para 97: a. Formatting requests according to the target API's specification (e.g., message structure, parameters like temperature, max tokens).
Para 98: b. Handling authentication (e.g., using API keys in headers).
Para 99: c. Parsing responses correctly.
Para 100: d. Implementing error handling for common API issues such as connection errors, timeouts, authentication failures, rate limiting (e.g., HTTP 429), and server errors (e.g., HTTP 5xx). Consider implementing basic retry logic or circuit breakers for transient errors.
Para 101: FR-API-008: The API shall implement request logging, including endpoint accessed, timestamp, and status code. Error logging should capture relevant details for debugging, ensuring sensitive information like API keys or full query/response payloads are masked or omitted.
Para 102: 3.4 Chat User Interface (UI) (FR-UI-XXX) The user interface provides the primary means for users to interact with the ADDSS MVP. For the MVP, it should be a functional, web-based chat application prioritizing core features over extensive customization or aesthetics. Design should consider usability principles for clinical software, emphasizing clarity and efficiency.
Para 103: FR-UI-001: The system shall provide a single-page web application (SPA) accessible via modern web browsers (Chrome, Firefox, Edge, Safari latest versions).
Para 104: FR-UI-002: The UI must feature a prominent text input area where users can type natural language questions or prompts. Pressing Enter or clicking a 'Send' button should submit the query to the backend API.
Para 105: FR-UI-003: The UI must display the ongoing conversation in a scrollable message area. This includes both user-submitted queries (and associated image context) and system-generated responses, ordered chronologically within the current browser session.
Para 106: FR-UI-004: User messages and system (AI) responses must be visually distinct within the conversation display (e.g., different background colors, alignment, or speaker labels).
Para 107: FR-UI-005: The UI must incorporate a mechanism to attach one or more image files to a message before it is sent. This should include:
Para 108: a. An interactive element (e.g., a paperclip icon button, drag-and-drop zone) near the text input area to trigger file selection from the user's local system.
Para 109: b. Support for selecting multiple files (JPG, PNG primarily).
Para 110: c. A preview area displaying thumbnails or filenames of the selected images before the message is submitted.
Para 111: d. An option for the user to remove any selected image(s) from the message before sending.
Para 112: e. Asynchronous upload handling: Upon file selection, the UI should initiate the upload to the backend API endpoint (FR-API-005) in the background, providing visual feedback to the user (e.g., upload progress indicator per file, success/failure status). Only after successful upload and receiving identifiers should the message (text + image identifiers) be sent via the chat endpoint (FR-API-002). (Leveraging established React file upload components/libraries is recommended ).
Para 113: FR-UI-006: (Stretch Goal) Investigate feasibility of supporting basic DICOM file uploads (e.g., .dcm files). If implemented, this may involve accepting the file type and associating its identifier, similar to JPG/PNG. Client-side parsing/rendering using libraries like cornerstone.js or dicom-parser for thumbnail generation or basic metadata display could be considered if time permits, but full diagnostic viewing capabilities are out of MVP scope. The primary goal remains associating the file context.
Para 114: FR-UI-007: System responses received from the backend API (FR-API-004) must be displayed clearly in the message area. The UI should handle potentially long responses gracefully (e.g., within scrollable bubbles). Basic markdown rendering (e.g., for lists, bold/italic text) should be supported if the LLM provides formatted output. If the API supports streaming (FR-API-004), the UI should display the response progressively as chunks arrive.
Para 115: FR-UI-008: The overall UI design should be simple, clean, and intuitive, prioritizing the core chat and upload functionality. Avoid unnecessary visual clutter. Development should leverage existing open-source React chat component libraries (e.g., assistant-ui , @minchat/react-chat-ui , or similar well-maintained options) to accelerate development and ensure standard chat features (like auto-scrolling) are included.
Para 116: FR-UI-009: The UI shall include a simple settings or configuration area (e.g., accessible via a settings icon or menu) where users can input and save the configuration details for external LLM providers as specified in FR-LLM-001 (API Endpoint URL, API Key, optional Preferred Model Name). Input fields for sensitive data like API keys should use appropriate masking (e.g., password input type).
Para 117: 3.5 LLM Configuration and Selection (FR-LLM-XXX) The system must provide flexibility in choosing the LLM used for response generation, defaulting to Google Cloud but allowing user configuration of external providers. This requires managing configurations and implementing selection logic.
Para 118: FR-LLM-001: The system must provide a mechanism (accessible via the UI as per FR-UI-009, potentially also via API for programmatic configuration) for users to specify connection details for external LLM providers. The required configuration parameters are:
Para 119: a. API Endpoint URL: The base URL for the external provider's chat completions API (e.g., https://openrouter.ai/api/v1).
Para 120: b. API Key: The authentication credential required by the external provider.
Para 121: The optional configuration parameter is:
Para 122: c. Preferred Model Name: A specific model identifier string recognized by the external provider (e.g., openai/gpt-4o, anthropic/claude-3.5-sonnet).
Para 123: FR-LLM-002: API keys entered by the user for external providers must be stored securely by the backend system. Storage should utilize encryption at rest, and keys should be managed through a secure mechanism (e.g., environment variables, cloud secrets manager) rather than being stored in plain text in databases or configuration files. Keys must not be exposed in logs or API responses. (See NFR-SEC-003).
Para 124: FR-LLM-003: Explicit Model Selection: If an external provider's URL and API Key are configured and saved, AND a 'Preferred Model Name' is also specified and saved, the backend API (FR-API-007) MUST use that specific model name when making requests to the configured API Endpoint URL, authenticating with the provided API Key.
Para 125: FR-LLM-004: Context-Aware Auto-Selection: If an external provider's URL and API Key are configured, BUT NO 'Preferred Model Name' is specified, the backend API must attempt automatic model selection based on the query context before making the request:
Para 126: a. Image Context Present: If the user's chat query (FR-API-002) includes associated image identifiers, the system should attempt to select a model suitable for multimodal input from the configured provider. (MVP implementation note: This may involve heuristics like trying standard vision model names, e.g., appending "-vision" or checking provider documentation if available, but robust auto-detection across arbitrary providers is complex and may default to a general model if a vision-specific one isn't easily identifiable).
Para 127: b. Text-Only Context: If the user's query has no associated images, the system should select a suitable general-purpose text generation or chat model from the provider. (MVP implementation note: This might involve using a common default model name or relying on the provider's default if no model is specified in the API call).
Para 128: FR-LLM-005: Default Provider: If NO external LLM provider configuration (URL and Key) is actively saved or enabled in the system, the backend API MUST default to using Google Cloud Vertex AI as the LLM provider.
Para 129: FR-LLM-006: Default Google Models: When defaulting to Google Cloud Vertex AI (as per FR-LLM-005), the following specific Gemini models available via the Vertex AI API shall be used:
Para 130: a. For text-only RAG queries (no associated images): gemini-1.5-flash-latest (or the current recommended equivalent balancing cost, performance, and capability).
Para 131: b. For queries with associated images (even if only image context/metadata is passed, not the image data itself): gemini-1.5-flash-latest (or the current recommended multimodal equivalent). The rationale is to use a model capable of potentially understanding multimodal context, aligning with the image upload feature and future analysis goals.
Para 132: FR-LLM-007: The backend API (specifically the RAG orchestration logic in FR-API-003 and the LLM client interaction logic in FR-API-007) must implement the conditional logic described in FR-LLM-003, FR-LLM-004, FR-LLM-005, and FR-LLM-006 to determine the correct LLM provider endpoint and model to use for each incoming chat request.Table 2: LLM Selection Logic Summary | External Provider Configured? | API Key Provided? | Preferred Model Specified? | Image(s) Attached to Query? | Action / LLM Used | | :---------------------------- | :---------------- | :------------------------- | :-------------------------- | :---------------------------------------------------------------------------------- | | No | N/A | N/A | No | Use Default Google Text Model (FR-LLM-006a, e.g., gemini-1.5-flash-latest) | | No | N/A | N/A | Yes | Use Default Google Multimodal Model (FR-LLM-006b, e.g., gemini-1.5-flash-latest) | | Yes | Yes | Yes (e.g., model-x) | No | Use External Provider (URL) with model-x via API Key (FR-LLM-003) | | Yes | Yes | Yes (e.g., model-y) | Yes | Use External Provider (URL) with model-y via API Key (FR-LLM-003) | | Yes | Yes | No | No | Use External Provider (URL) via API Key, attempt auto-select Text model (FR-LLM-004b) | | Yes | Yes | No | Yes | Use External Provider (URL) via API Key, attempt auto-select Vision model (FR-LLM-004a)| | Yes | No | N/A | N/A | Error / Fallback to Default Google (Treat as 'No External Provider Configured') |
Para 133: 4. Non-Functional Requirements (MVP Focus)
Para 134: Non-functional requirements define the quality attributes of the system. For the MVP, focus is placed on core accuracy, performance, and security aspects necessary to demonstrate feasibility and provide a usable foundation.
Para 135: 4.1 Accuracy (NFR-ACC-XXX) Accuracy targets for the MVP are set to establish a baseline for the core AI/ML components. Perfect accuracy is unattainable, especially with complex inputs like varied PDF structures and nuanced natural language.
Para 136: NFR-ACC-001: PDF Text Extraction Accuracy: The system must achieve >95% character-level accuracy for text extracted from clean, digitally-born PDF sections with standard single-column layouts. For scanned pages processed via OCR, the target is >85% word accuracy on clear input. Accuracy on complex tables or heavily stylized layouts may be lower in the MVP.
Para 137: NFR-ACC-002: KG Entity Extraction (NER) Accuracy: The NER process should achieve an F1-score of > 0.80 for the primary entity types (Disease, Procedure, Symptom) when evaluated against a manually annotated test set derived from the source textbooks.
Para 138: NFR-ACC-003: KG Relation Extraction (RE) Accuracy: The RE process should achieve an F1-score of > 0.70 for the primary relation types (e.g., causes, treats) on the annotated test set. Relation extraction is typically more challenging than NER.
Para 139: NFR-ACC-004: RAG Response Relevance & Grounding: For a predefined set of test queries representative of expected user interactions, >80% of the generated responses must be assessed (qualitatively by domain experts for MVP) as directly relevant to the query and factually grounded in the retrieved context from the ingested textbooks. Responses should avoid hallucination or contradiction of source material.
Para 140: 4.2 Performance (NFR-PRF-XXX) Performance targets ensure a reasonably responsive user experience for the MVP. These targets exclude initial cold-start times for models or services where applicable.
Para 141: NFR-PRF-001: PDF Processing Time: The average time to process a single page of a PDF (including text extraction, layout analysis, OCR if needed, basic table extraction) should be less than 10 seconds, measured after any initial model loading.
Para 142: NFR-PRF-002: KG Construction Time: While bulk loading of the initial KG from multiple textbooks is an offline process and less time-critical for the MVP, the system should be capable of processing a single new medium-sized document (e.g., 50 pages) and updating the KG within a reasonable timeframe (e.g., under 1 hour), suitable for non-real-time updates.
Para 143: NFR-PRF-003: Chat Query Response Time (End-to-End): The 90th percentile (P90) latency for a user submitting a chat query and receiving the beginning of the response stream in the UI should be less than 10 seconds. This includes API processing, context retrieval, LLM inference (excluding initial model cold start), and network latency. The first token of a streaming response should ideally appear in the UI within 3 seconds.
Para 144: NFR-PRF-004: Image Upload Speed: Successfully uploading a single image file (< 5MB) via the UI should complete (from user action to API confirmation) in under 5 seconds over a typical broadband internet connection (e.g., 25 Mbps download/5 Mbps upload). The system should handle concurrent uploads of multiple small images without significant degradation..Table 3: MVP Accuracy & Performance Targets Summary | Requirement Area | Metric | Target Value (MVP) | Notes | | :-------------------------------- | :--------------------- | :------------------------------------- | :------------------------------------------------------------------------------------------------ | | PDF Text Extraction (Clean Text) | Character Accuracy | >95% | Assessed on benchmark set of digital PDFs. | | PDF Text Extraction (Scanned/OCR) | Word Accuracy | >85% | Assessed on benchmark set of clear scanned PDFs. | | KG NER (Key Entities) | F1-Score | >0.80 | Assessed on manually annotated evaluation set (e.g., Disease, Procedure, Symptom). | | KG RE (Key Relations) | F1-Score | >0.70 | Assessed on manually annotated evaluation set (e.g., causes, treats). | | RAG Response Relevance | Qualitative Assessment | >80% relevant/grounded | Manual review of responses to representative test queries by domain experts. | | Chat Query Response Time | P90 Latency | < 10s (excluding cold start) | Measured end-to-end (UI send to first token received). Dependent on chosen LLM provider. | | Image Upload (<5MB) | Time to complete | < 5s | Measured from UI interaction to successful upload confirmation from API over standard broadband. |
Para 145: 4.3 Security (NFR-SEC-XXX) Security is a critical consideration, even for an MVP, given the nature of the data (potentially copyrighted textbooks, user queries that might contain sensitive terms, uploaded images, API keys). Basic security measures must be implemented from the outset.
Para 146: NFR-SEC-001: Textbook Data Security: Uploaded PDF files must be stored in a secure, private location (e.g., a dedicated, access-controlled cloud storage bucket) with encryption at rest enabled. Access to these files should be restricted to authorized system processes only.
Para 147: NFR-SEC-002: User Query & Image Security: All communication between the client UI and the backend API must be encrypted using HTTPS/TLS. User queries and temporarily stored uploaded images must be protected with appropriate access controls and encryption at rest within the temporary storage (e.g., secure cloud storage). A data retention policy must be defined and implemented for temporarily stored images (e.g., automatically delete after 24 hours or upon session termination). While not handling formal PHI, practices should align with principles minimizing future compliance gaps for regulations like HIPAA or GDPR.
Para 148: NFR-SEC-003: API Key Security: API keys provided by users for external LLM providers (FR-LLM-001b) are highly sensitive and MUST NOT be stored in plaintext. They must be stored using secure methods, such as a dedicated secrets management service (e.g., Google Secret Manager, AWS Secrets Manager, Azure Key Vault) or securely configured environment variables. Keys must never be hardcoded in source code, committed to version control, or included in logs. Access to retrieve these keys by the backend API must be tightly controlled.
Para 149: NFR-SEC-004: Infrastructure Security: All cloud infrastructure components (compute instances/containers, databases, storage buckets, API gateways) must be configured with basic security best practices, including network firewalls restricting unnecessary inbound/outbound traffic, use of non-default passwords, principle of least privilege for service accounts, and keeping system software patched.
Para 150: NFR-SEC-005: Input Validation: The backend API must perform basic input validation on all incoming requests, including chat messages and file uploads. This includes checking for reasonable data types and lengths for text inputs, and enforcing file size limits and basic MIME type checks for uploads to mitigate risks like denial-of-service or upload of malicious files.
Para 151: 5. Data Requirements
Para 152: Acquiring appropriate data is essential for developing, testing, and evaluating the ADDSS MVP.
Para 153: 5.1 Input Data
Para 154: DATA-001: Dental Textbooks: A collection of at least 5 to 10 representative dental textbook PDF files is required. This collection should span different dental specialties (e.g., periodontics, endodontics, oral surgery, restorative dentistry) and include a variety of content types: dense text chapters, pages with embedded images and associated captions, and pages containing tables. The set should ideally include both digitally native PDFs and scanned PDFs to test OCR capabilities. Note: Copyright restrictions must be strictly adhered to.
Para 155: DATA-002: Sample Dental Images: A set of 20-30 sample images representative of typical clinical uploads is needed for testing the image upload feature (FR-UI-005). This set must include common web formats (JPG, PNG). Examples should cover intraoral photographs, panoramic X-rays, and periapical X-rays. If DICOM upload support (FR-UI-006) is attempted, corresponding sample .dcm files are also required. Publicly available datasets can be explored, but require careful verification of licensing terms, anonymization status, and ethical approvals. Using internally generated, anonymized samples or commercially licensed stock images specifically for testing purposes is preferred for the MVP to avoid compliance issues.
Para 156: 5.2 Evaluation Data
Para 157: DATA-003: KG Evaluation Data: To assess the accuracy of the KG construction process (NFR-ACC-002, NFR-ACC-003), a subset of text extracted from the input textbooks will need to be manually annotated. This involves identifying spans of text corresponding to target entities (Disease, Symptom, Procedure, etc.) and labeling the relationships between them. The size and scope of this annotation effort need to be planned as part of the MVP development cycle.
Para 158: DATA-004: RAG Evaluation Queries: A set of realistic test queries (e.g., 30-50 questions) covering various topics present in the textbooks is required to evaluate the relevance and grounding of the RAG system's responses (NFR-ACC-004). For each query, expected key information or relevance judgments for potential retrieved passages might be needed for assessment.
Para 159: 5.3 Data Considerations
Para 160: DATA-005: Copyright Compliance: The use of dental textbooks for ingestion must comply with copyright law. Obtain necessary permissions or licenses, or use textbooks that are explicitly licensed for this type of use (e.g., open access textbooks, or with publisher agreement). Unauthorized use of copyrighted material is prohibited.
Para 161: DATA-006: Image Data Ethics and Licensing: If using any external image datasets, rigorously verify the associated license permits use in this type of application. Confirm that appropriate patient consent and ethical review board approvals were obtained for the original data collection, and that data has been properly anonymized/de-identified according to relevant standards (e.g., HIPAA). Avoid using data where provenance, licensing, or ethical clearance is unclear.
Para 162: 6. Technology Stack (MVP Recommendations)
Para 163: The following technology stack is recommended for the ADDSS MVP, balancing performance, developer productivity, ecosystem support for AI/ML tasks, and alignment with specified requirements like the default LLM provider.
Para 164: 6.1 Backend
Para 165: TECH-001: Programming Language: Python 3.9+. Chosen for its extensive ecosystem of libraries for AI/ML, NLP, data manipulation, and web development.
Para 166: TECH-002: Web Framework: FastAPI. Recommended due to its high performance, native asynchronous support (crucial for handling I/O bound tasks like LLM calls and file I/O efficiently), automatic data validation via Pydantic, and auto-generated API documentation (Swagger UI).
Para 167: TECH-003: PDF Processing Libraries: A combination is likely necessary:
Para 168: PyMuPDF (Fitz): For robust extraction of text, basic layout information, images, and metadata from various PDF types.
Para 169: Tesseract (via pytesseract wrapper): For OCR functionality on scanned pages or image-based PDFs.
Para 170: pdfplumber or Camelot-py: For focused table extraction, to be evaluated based on the complexity of tables in the target textbooks.
Para 171: TECH-004: KG Construction/NLP:
Para 172: Option A: Leverage LLMs for NER and RE via frameworks like LangChain or LlamaIndex , potentially using modules like llm-graph-transformer or custom prompts.
Para 173: Option B: Use traditional NLP libraries like spaCy (potentially with domain-specific models like scispaCy ) for NER, combined with rule-based or simpler ML models for RE.
Para 174: Consider RDFLib if adherence to RDF standards for the KG is a strict requirement, possibly in conjunction with a Neo4j backend using rdflib-neo4j.
Para 175: TECH-005: LLM Interaction:
Para 176: Google Cloud Vertex AI: Use the official google-cloud-aiplatform Python SDK.
Para 177: External Providers (e.g., OpenRouter): Use the official openai Python SDK, configuring the base_url and api_key parameters appropriately.
Para 178: Abstraction: Consider using LangChain or LlamaIndex which provide abstractions over multiple LLM providers, potentially simplifying the management of different APIs.
Para 179: HTTP Client: requests library for basic HTTP interactions if direct calls are needed.
Para 180: TECH-006: Asynchronous Task Handling: Utilize FastAPI's built-in async/await for handling concurrent API requests and LLM calls. Employ FastAPI's BackgroundTasks for potentially long-running operations like initial PDF processing or KG updates triggered via API.
Para 181: 6.2 Database
Para 182: TECH-007: Primary/KG/Vector Storage: PostgreSQL (version 14+) with the pgvector extension. This provides a versatile solution for the MVP, capable of storing:
Para 183: Structured metadata (e.g., about PDFs, configuration).
Para 184: KG entities and relationships (using relational tables with foreign keys, or potentially JSONB columns for flexibility).
Para 185: Text chunks extracted from PDFs.
Para 186: Vector embeddings (via pgvector) for semantic search in the RAG process.
Para 187: TECH-008: Alternative Graph Database: Neo4j is a strong alternative if complex graph traversals and analytics are anticipated as key features beyond the MVP. Performance comparisons between Neo4j and competitors like TigerGraph exist , but PostgreSQL with pgvector is likely sufficient and simpler for the MVP's RAG focus.
Para 188: TECH-009: Dedicated Vector Database: While options like Milvus , Weaviate , or Pinecone offer specialized vector search capabilities, integrating vector search within PostgreSQL using pgvector simplifies the MVP architecture and reduces operational overhead. A dedicated vector DB can be considered later if performance bottlenecks arise.
Para 189: 6.3 Frontend
Para 190: TECH-010: Framework: React. Chosen for its popularity, large developer community, component-based architecture, and availability of pre-built UI libraries, including chat components. Alternatives like Flutter could be considered if cross-platform mobile deployment is a near-term goal, but React is standard for web SPAs.
Para 191: TECH-011: Chat UI Components: Utilize a well-maintained open-source React chat library that supports customization and ideally has built-in or easily integrable file attachment capabilities. Examples include:
Para 192: assistant-ui (integrates with AI SDK, LangGraph, supports attachments)
Para 193: @minchat/react-chat-ui (open source, customizable)
Para 194: Commercial options like Stream Chat React or CometChat UI Kit (offer more features but incur costs).
Para 195: Libraries supporting file uploads like react-chat-widget derivatives or PubNub components might also be adapted.
Para 196: TECH-012: DICOM Handling (Client-side, if FR-UI-006 is pursued): Libraries like dicom-parser for extracting basic metadata or cornerstone.js for potential basic rendering/thumbnail generation. Viewers like DWV (DICOM Web Viewer) integrated into React are also options, but likely too complex for MVP's limited scope. pydicom is a backend Python library for DICOM processing.
Para 197: 6.4 Cloud Platform
Para 198: TECH-013: Default LLM & Hosting: Google Cloud Platform (GCP). Chosen primarily because the default LLM is specified as Google Vertex AI Gemini. Hosting the application on GCP simplifies integration with Vertex AI and other GCP services.
Para 199: TECH-014: Specific GCP Services:
Para 200: Compute: Google Cloud Run (for containerized FastAPI backend - serverless, scalable).
Para 201: Storage: Google Cloud Storage (for storing uploaded PDFs and temporary images).
Para 202: Database: Google Cloud SQL for PostgreSQL (managed PostgreSQL service) or AlloyDB for PostgreSQL (higher performance option).
Para 203: LLM: Google Vertex AI API (for accessing Gemini models).
Para 204: Secrets Management: Google Secret Manager (for securely storing external API keys).
Para 205: Table 4: Recommended MVP Technology Stack Summary | Layer | Component | Recommendation | Justification/Notes | | :----------------- | :------------------------ | :-------------------------------------------- | :---------------------------------------------------------------------------------- | | Backend Language | Python | Python 3.9+ | Rich AI/ML ecosystem, mature libraries, strong community support. | | Backend Framework | Web Framework | FastAPI | High performance, native async support, auto-docs, Pydantic validation. | | Backend | PDF Processing | PyMuPDF + Tesseract + pdfplumber/Camelot | Combination for robust text, layout, OCR, and table extraction. | | Backend | KG Construction / NLP | LangChain/LlamaIndex or spaCy | Frameworks for LLM-based extraction or standard NLP pipeline. | | Backend | LLM Interaction | google-cloud-aiplatform SDK, openai SDK | Official SDKs for interacting with Vertex AI and external (OpenRouter) APIs. | | Database | Primary/KG/Vector Storage | PostgreSQL 14+ with pgvector extension | Versatile storage for structured data, KG relations, text chunks, vectors. | | Frontend Framework | UI Framework | React | Popular, component-based, large ecosystem, many UI libraries. | | Frontend | Chat UI | Open Source Library (e.g., assistant-ui) | Accelerates development, provides core chat features with attachment support. | | Cloud Platform | Default LLM Provider | Google Vertex AI (Gemini models) | Specified default, strong performance, multimodal capabilities. | | Cloud Platform | Hosting Infrastructure | GCP (Cloud Run, Cloud Storage, Cloud SQL/AlloyDB) | Aligns with default LLM, offers scalable managed services. |
Para 206: 7. Future Considerations
Para 207: The ADDSS MVP establishes a foundational capability. Subsequent development phases should focus on enhancing clinical utility, user experience, and robustness. Key areas for future consideration include:
Para 208: 7.1 Image Analysis Integration: The most significant next step involves moving beyond simple image upload to AI-driven analysis. This requires integrating computer vision models or multimodal LLMs capable of interpreting dental images (e.g., detecting caries, bone loss, periapical lesions on radiographs). The flexible LLM architecture in the MVP is designed to facilitate the incorporation of such multimodal models. Output from image analysis could then be integrated into the RAG context or presented alongside text-based answers.
Para 209: 7.2 Enhanced Knowledge Graph and Reasoning: The KG built in the MVP is foundational. Future work should involve expanding the schema to include more granular entities and relationships (e.g., specific drug interactions, detailed procedural steps, patient risk factors), improving the accuracy of NER and RE, potentially through fine-tuning models or more sophisticated LLM prompting, and implementing more advanced graph reasoning algorithms (e.g., pathfinding, link prediction) to enable more complex decision support beyond direct information retrieval.
Para 210: 7.3 Explainable AI (XAI): For clinical acceptance and trust, incorporating XAI features is crucial. This could involve highlighting the specific text passages or KG elements used by the RAG system to generate an answer, or visualizing feature importance (e.g., using SHAP or LIME techniques) if predictive models are added later. Designing effective explanation interfaces (XUIs) for clinicians is a key challenge.
Para 211: 7.4 Clinical Workflow Integration: To be truly useful in practice, the system needs integration with existing clinical workflows. This likely involves integration with Electronic Health Record (EHR) or Practice Management Software (PMS) systems, potentially using standards like HL7 FHIR for data exchange.
Para 212: 7.5 User Management and Personalization: Implementing robust user authentication, role-based access control, persistent chat history tied to users, and personalization features (e.g., saving preferences, tailoring information) will enhance usability and security.
Para 213: 7.6 Regulatory Compliance and Clinical Validation: If the system evolves to provide direct diagnostic suggestions or treatment recommendations, it will likely be classified as Software as a Medical Device (SaMD) and require rigorous clinical evaluation and validation to meet regulatory requirements in target markets (e.g., FDA in the US , EU MDR , CDSCO in India ). This involves demonstrating safety, efficacy, and adherence to standards like IEC 62304. Strict adherence to data privacy regulations like HIPAA and GDPR will be mandatory, requiring features like explicit consent, data minimization, robust security, and audit trails. India's Digital Personal Data Protection (DPDP) Act, 2023, and initiatives like the Ayushman Bharat Digital Mission (ABDM) also shape the compliance landscape.
Para 214: 8. Conclusion
Para 215: This Product Requirements Document outlines the scope, objectives, and detailed requirements for the Minimum Viable Product (MVP) of the AI-Powered Dental Decision Support System. The MVP focuses on establishing the core technical pipeline: ingesting dental textbook PDFs, constructing a foundational knowledge graph, enabling RAG-based chat querying with image context upload, and providing flexible LLM configuration defaulting to Google Cloud Vertex AI.
Para 216: Successful completion of this MVP will validate the feasibility of leveraging AI to structure and access knowledge from dental literature, providing a critical foundation for future development. Subsequent iterations will build upon this base, incorporating image analysis, enhancing KG reasoning, integrating XAI for transparency, enabling clinical workflow integration, and addressing the rigorous requirements for clinical validation and regulatory compliance necessary for deployment in real-world healthcare settings. The defined functional and non-functional requirements, along with the recommended technology stack, provide a clear roadmap for the development team to deliver this foundational MVP.
Para 217: Works cited
Para 218: 1. The 2025 Guide to Document Data Extraction using AI, https://www.cradl.ai/post/document-data-extraction-using-ai 2. Extraction of Structured Data From EHR Using NLP | 47Billion, https://47billion.com/blog/extraction-of-structured-data-from-electronic-health-records-using-natural-language-processing/ 3. How to Process Unstructured PDFs with Python in 2025 - UnDatasIO, https://undatas.io/blog/posts/how-to-process-unstructured-pdfs-with-python-in-2025/ 4. A comprehensive large scale biomedical knowledge graph for AI powered data driven biomedical research - National Institutes of Health (NIH), https://pmc.ncbi.nlm.nih.gov/articles/PMC10760044/ 5. Automatic Cause-Effect Relation Extraction from Dental Textbooks Using BERT | Request PDF - ResearchGate, https://www.researchgate.net/publication/356637668_Automatic_Cause-Effect_Relation_Extraction_from_Dental_Textbooks_Using_BERT 6. Building Knowledge Graphs from Unstructured Texts: Applications and Impact Analyses in Cybersecurity Education - MDPI, https://www.mdpi.com/2078-2489/13/11/526 7. KRAGEN: a knowledge graph-enhanced RAG framework for biomedical problem solving using large language models, https://pmc.ncbi.nlm.nih.gov/articles/PMC11164829/ 8. arXiv:2502.14614v2 [cs.CL] 13 Mar 2025, https://arxiv.org/pdf/2502.14614 9. Step-by-Step Guide to Building Knowledge Graph RAG Systems - PageOn.ai, https://www.pageon.ai/blog/knowledge-graph-llm-knowledge-graph-rag 10. Bridging Modalities: Multimodal RAG for Advanced Information Retrieval - InfoQ, https://www.infoq.com/articles/multimodal-rag-advanced-information-retrieval/ 11. MULTIMODAL RAG: THE FUTURE OF AI AND DATA - Kanerika, https://kanerika.com/blogs/multimodal-rag/ 12. Get started with the Gemini API using the Vertex AI in Firebase SDKs - Google, https://firebase.google.com/docs/vertex-ai/get-started 13. Getting Started with the Gemini API in Vertex AI | Google Cloud Skills Boost, https://www.cloudskillsboost.google/focuses/83264?parent=catalog 14. OpenRouter API Reference | Complete API Documentation, https://openrouter.ai/docs/api-reference/overview 15. OpenRouter Quickstart Guide | Developer Documentation ..., https://openrouter.ai/docs/quickstart 16. [D] Knowledge Graph Extraction from Unstructured Medical Texts : r/MachineLearning, https://www.reddit.com/r/MachineLearning/comments/193s7dq/d_knowledge_graph_extraction_from_unstructured/ 17. A Guide to PDF Extraction Libraries in Python - Metric Coders, https://www.metriccoders.com/post/a-guide-to-pdf-extraction-libraries-in-python 18. Neo4j GraphRAG Python Package - Neo4j Labs, https://neo4j.com/labs/genai-ecosystem/graphrag-python/ 19. Enhancing LLM Generation with Knowledge Hypergraph for Evidence-Based Medicine, https://www.researchgate.net/publication/390113984_Enhancing_LLM_Generation_with_Knowledge_Hypergraph_for_Evidence-Based_Medicine 20. An Overview of AI Hallucinations with RAG and Knowledge Graphs | DigitalOcean, https://www.digitalocean.com/community/conceptual-articles/ai-hallucinations-with-rag-and-knowledge-graphs 21. Multi-Modal RAG — LlamaIndex - Build Knowledge Assistants over your Enterprise Data, https://www.llamaindex.ai/blog/multi-modal-rag-621de7525fea 22. FastAPI Resiliency: Circuit Breakers, Rate Limiting, and External API Management, https://www.aritro.in/post/fastapi-resiliency-circuit-breakers-rate-limiting-and-external-api-management/ 23. FastAPI and LLMs: A Great Choice for Serving Language models - Niuro, https://niuro.io/fastapi-and-llms-a-great-choice-for-serving-language-models/ 24. Gemini API quickstart | Google AI for Developers, https://ai.google.dev/gemini-api/docs/quickstart 25. Explainable AI in Healthcare: Systematic Review of Clinical Decision Support Systems, https://www.medrxiv.org/content/10.1101/2024.08.10.24311735v1.full-text 26. Explainable AI (XAI) in Healthcare: Interpretable Models for Clinical Decision Support - ijcsitr, https://ijcsitr.com/index.php/home/<USER>/view/IJCSITR_2024_05_02_04 27. (PDF) Explainable AI for Clinical Decision Support - ResearchGate, https://www.researchgate.net/publication/383920571_Explainable_AI_for_Clinical_Decision_Support 28. (PDF) Applications of Voice User Interfaces in Clinical Settings - ResearchGate, https://www.researchgate.net/publication/370949881_Applications_of_Voice_User_Interfaces_in_Clinical_Settings 29. Voice applications in clinical research powered by AI on AWS – Part 1: Architecture and design considerations, https://aws.amazon.com/blogs/industries/voice-applications-in-clinical-research-powered-by-ai-on-aws-part-1-architecture-and-design-considerations/ 30. A User-Friendly Guide to Voice User Interfaces (VUI) - Codiant, https://codiant.com/blog/guide-to-voice-user-interfaces-vui/ 31. How can your SaMD pass the clinical evaluation phase in the EU? - Extra Horizon, https://www.extrahorizon.com/insights/blog-posts/how-can-your-samd-pass-the-clinical-evaluation-phase-with-flying-colours-in-eu-markets 32. Full article: Artificial intelligence in medical device software and high-risk medical devices – a review of definitions, expert recommendations and regulatory initiatives, https://www.tandfonline.com/doi/full/10.1080/17434440.2023.2184685 33. Medical device & diagnostics - cdsco, https://cdsco.gov.in/opencms/opencms/en/Medical-Device-Diagnostics/ 34. FDA AI/ML Enabled Software as a Medical Device (SaMD) | Research, https://www.research.uky.edu/uploads/ori-fda-aiml-enabled-software-medical-device-samd-pdf 35. Artificial Intelligence and Machine Learning in Software as a Medical Device - FDA, https://www.fda.gov/medical-devices/software-medical-device-samd/artificial-intelligence-and-machine-learning-software-medical-device 36. Clinical Validation vs. Clinical Evaluation - Regulatory knowledge for medical devices, https://blog.johner-institute.com/regulatory-affairs/clinical-validation/ 37. Regulation of Software as Medical Device (SaMD) in India - Freyr Solutions, https://www.freyrsolutions.com/blog/regulation-of-software-as-medical-device-samd-in-india 38. GUIDANCE ON SaMD REGULATIONS - LIFECYCLE APPROACH - APACMed, https://apacmed.org/wp-content/uploads/2022/01/SaMD-Guidance-Document.pdf 39. Software as Medical Device (SaMD) Classification in India - Morulaa HealthTech, https://morulaa.com/software-as-medical-device-india/ 40. FDA Regulation of Predictive Clinical Decision-Support Tools: What Does It Mean for Hospitals? - PMC, https://pmc.ncbi.nlm.nih.gov/articles/**********/ 41. CDSCO Import License Registration Approval Process India - Morulaa HealthTech, https://morulaa.com/cdsco-import-license-medical-device-registration-india/ 42. Expert discusses implications of EU AI Act 'high risk' provisions on medical devices - RAPS, https://www.raps.org/news-and-articles/news-articles/2024/10/expert-discusses-implications-of-eu-ai-act-%E2%80%98high-r 43. Clinical Decision Support Software Frequently Asked Questions (FAQs) - FDA, https://www.fda.gov/medical-devices/software-medical-device-samd/clinical-decision-support-software-frequently-asked-questions-faqs 44. Clinical Decision Support Software - Guidance for Industry and Food and Drug Administration Staff, https://www.fda.gov/media/109618/download 45. Clinical Decision Support Software - Guidance - FDA, https://www.fda.gov/regulatory-information/search-fda-guidance-documents/clinical-decision-support-software 46. Approval of AI-based medical devices in Europe - VDE, https://www.vde.com/topics-en/health/consulting/approval-of-ai-based-medical-devices-in-europe 47. (PDF) Building a Medical Knowledge Graph to Enhance the Fraud, Waste and Abuse Detection on Claim Data (Preprint) - ResearchGate, https://www.researchgate.net/publication/341705787_Building_a_Medical_Knowledge_Graph_to_Enhance_the_Fraud_Waste_and_Abuse_Detection_on_Claim_Data_Preprint 48. A comprehensive large scale biomedical knowledge graph for AI powered data driven biomedical research - PubMed, https://pubmed.ncbi.nlm.nih.gov/38168218/ 49. Adaptive Knowledge Graphs Enhance Medical Question Answering: Bridging the Gap Between LLMs and Evolving Medical Knowledge - arXiv, https://arxiv.org/html/2502.13010v1 50. [2501.02460] Towards Omni-RAG: Comprehensive Retrieval-Augmented Generation for Large Language Models in Medical Applications - arXiv, https://arxiv.org/abs/2501.02460 51. MRD-RAG: Enhancing Medical Diagnosis with Multi-Round Retrieval-Augmented Generation - arXiv, https://arxiv.org/html/2504.07724v1 52. [2502.04413] MedRAG: Enhancing Retrieval-augmented Generation with Knowledge Graph-Elicited Reasoning for Healthcare Copilot - arXiv, https://arxiv.org/abs/2502.04413 53. Biomedical knowledge graph-optimized prompt generation for large language models, https://pmc.ncbi.nlm.nih.gov/articles/PMC11441322/ 54. MedRAG: Enhancing Retrieval-augmented Generation with Knowledge Graph-Elicited Reasoning for Healthcare Copilot - arXiv, https://arxiv.org/html/2502.04413v1 55. Large Language Models and Medical Knowledge Grounding for Diagnosis Prediction, https://www.medrxiv.org/content/10.1101/2023.11.24.23298641v2.full 56. (PDF) Publicly Available Dental ImageDatasets for Artificial Intelligence - ResearchGate, https://www.researchgate.net/publication/385041698_Publicly_Available_Dental_ImageDatasets_for_Artificial_Intelligence 57. Comprehensive Insights into Artificial Intelligence for Dental Lesion Detection: A Systematic Review - PMC, https://pmc.ncbi.nlm.nih.gov/articles/PMC11640338/ 58. Dental Images Recognition Technology and Applications: A Literature Review - MDPI, https://www.mdpi.com/2076-3417/10/8/2856 59. Multimodal RAG with Gemini - Pathway, https://pathway.com/blog/gemini-rag 60. An Easy Introduction to Multimodal Retrieval-Augmented Generation - NVIDIA Developer, https://developer.nvidia.com/blog/an-easy-introduction-to-multimodal-retrieval-augmented-generation/ 61. Using AI to extract Structured Data from PDFs - Victory Square Partners, https://victorysquarepartners.com/using-ai-to-extract-structured-data-from-pdfs/ 62. Why a PDF Text Extraction Software is Key for Quality AI Text Training Data - Encord, https://encord.com/blog/pdf-text-extraction-software/ 63. Parsing Tables in PDF Using Python: A Comprehensive Guide - UnDatasIO, https://undatas.io/blog/posts/parsing-tables-in-pdf-using-python-a-comprehensive-guide/ 64. Challenges You Will Face When Parsing PDFs With Python - Seattle Data Guy, https://www.theseattledataguy.com/challenges-you-will-face-when-parsing-pdfs-with-python-how-to-parse-pdfs-with-python/ 65. An Evaluation of Python PDF to Text Parser Libraries - Unstract, https://unstract.com/blog/evaluating-python-pdf-to-text-libraries/ 66. Comparing 6 Frameworks for Rule-based PDF parsing - AI Bites, https://www.ai-bites.net/comparing-6-frameworks-for-rule-based-pdf-parsing/ 67. Python Libraries to Extract Tables From PDF: A Comparison - Unstract, https://unstract.com/blog/extract-tables-from-pdf-python/ 68. Leveraging Medical Knowledge Graphs and Large Language Models for Enhanced Mental Disorder Information Extraction - MDPI, https://www.mdpi.com/1999-5903/16/8/260 69. (PDF) Dental Restorative Material Ontology (DrMO) - ResearchGate, https://www.researchgate.net/publication/376744326_Dental_Restorative_Material_Ontology_DrMO 70. An ontology-based method for secondary use of electronic dental ..., https://pmc.ncbi.nlm.nih.gov/articles/PMC3845770/ 71. Dentist - Schema.org Type, https://schema.org/Dentist 72. an empirical comparison of neo4j and tigergraph databases for network centrality, https://www.researchgate.net/publication/370424122_AN_EMPIRICAL_COMPARISON_OF_NEO4J_AND_TIGERGRAPH_DATABASES_FOR_NETWORK_CENTRALITY 73. Diagnosis and Treatment Knowledge Graph Modeling Application Based on Chinese Medical Records - MDPI, https://www.mdpi.com/2079-9292/12/16/3412 74. Rdflib-Neo4j: A New Era in RDF Integration for Neo4j, https://neo4j.com/blog/developer/rdflib-neo4j-rdf-integration-neo4j/ 75. Build applications with Neo4j and Python - Neo4j Python Driver Manual, https://neo4j.com/docs/python-manual/current/ 76. A PostgreSQL Interface for FHIR Data using the CData ODBC Driver, https://www.cdata.com/kb/tech/fhir-odbc-postgresql-fdw.rst 77. FHIR Storage (Relational) Module - Smile CDR Documentation, https://smilecdr.com/docs/fhir_storage_relational/fhir_storage_relational_module.html 78. fhirbase: FHIR persistence in PostgreSQL - GitHub Gist, https://gist.github.com/14a4c21624c549ff79b8 79. Fhirbase - Health Samurai, https://www.health-samurai.io/fhir-database 80. Database Schema - HAPI FHIR Documentation, https://hapifhir.io/hapi-fhir/docs/server_jpa/schema.html 81. How do I choose between Pinecone, Weaviate, Milvus, and other vector databases?, https://milvus.io/ai-quick-reference/how-do-i-choose-between-pinecone-weaviate-milvus-and-other-vector-databases 82. Vector Database Comparison: Pinecone vs Weaviate vs Qdrant vs FAISS vs Milvus vs Chroma (2025) | LiquidMetal AI, https://liquidmetal.ai/casesAndBlogs/vector-comparison/ 83. Fastapi File Upload Best Practices | Restackio, https://www.restack.io/p/ai-python-answer-fastapi-file-upload-best-practices-cat-ai 84. Choosing Between LlamaIndex and LangChain: Finding the Right Tool for Your AI Application | DigitalOcean, https://www.digitalocean.com/community/tutorials/llamaindex-vs-langchain-for-deep-learning 85. Llamaindex vs Langchain: What's the difference? - IBM, https://www.ibm.com/think/topics/llamaindex-vs-langchain 86. API Authentication | OpenRouter OAuth and API Keys, https://openrouter.ai/docs/api-reference/authentication 87. The Hidden Bottleneck in LLM Streaming: Function Calls (And How to Fix It), https://dev.to/louis-sanna/the-hidden-bottleneck-in-llm-streaming-function-calls-and-how-to-fix-it-1622 88. How to Handle File Uploads with Python and FastAPI - Ionx Solutions Blog, https://blog.ionxsolutions.com/p/file-uploads-with-python-fastapi/ 89. Request Files - FastAPI, https://fastapi.tiangolo.com/tutorial/request-files/ 90. API Key Best Practices: Keeping Your Keys Safe and Secure | Anthropic Help Center, https://support.anthropic.com/en/articles/9767949-api-key-best-practices-keeping-your-keys-safe-and-secure 91. API Key Security Best Practices: Secure Sensitive Data, https://www.legitsecurity.com/blog/api-key-security-best-practices 92. Best Practices for API Key Safety | OpenAI Help Center, https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety 93. Google Gen AI SDK - Gemini API, https://ai.google.dev/gemini-api/docs/sdks 94. Securing Your FastAPI Web Service: Best Practices and Techniques - LoadForge Guides, https://loadforge.com/guides/securing-your-fastapi-web-service-best-practices-and-techniques 95. UX design in healthcare: Challenges & trends in 2025 | Lyssna, https://www.lyssna.com/blog/healthcare-ux-design/ 96. Clinical decision support systems in orthodontics: A narrative review ..., https://pmc.ncbi.nlm.nih.gov/articles/PMC8988880/ 97. Four principles for user interface design of computerised clinical decision support systems, https://pubmed.ncbi.nlm.nih.gov/21685612/ 98. EHR Interface Design Principles, UX, And Usability Challenges - Fuselab Creative, https://fuselabcreative.com/ehr-interface-design-principles-ux-and-usability-challenges/ 99. MinChatHQ/react-chat-ui: Build your own chat UI with React ... - GitHub, https://github.com/MinChatHQ/react-chat-ui 100. assistant-ui/assistant-ui: Typescript/React Library for AI ... - GitHub, https://github.com/assistant-ui/assistant-ui 101. Wintjen/react-chat-widget-file-uploder - GitHub, https://github.com/Wintjen/react-chat-widget-file-uploder 102. React Chat Tutorial - GetStream.io, https://getstream.io/chat/react-chat/tutorial/ 103. File upload for PubNub Chat Components for React Native, https://www.pubnub.com/docs/chat/components/react-native/file-upload 104. React Chat UI Kit - Pre-built react chat components and UI library - CometChat, https://www.cometchat.com/react-chat-ui-kit 105. 5 Steps to Integrating a DICOM Viewer in React for Healthcare Apps - Sparkle Web, https://www.sparkleweb.in/blog/5_steps_to_integrating_a_dicom_viewer_in_react_for_healthcare_apps 106. webnamics/u-dicom-viewer: A simple web browser DICOM viewer for any device. - GitHub, https://github.com/webnamics/u-dicom-viewer 107. Configure and Run the LEADTOOLS Medical Web Viewer - React JS, https://www.leadtools.com/help/sdk/tutorials/html5-reactjs-configure-and-run-the-medical-web-viewer.html 108. Add web DICOM viewer to a React and ASP.NET Core application - VintaSoft, https://www.vintasoft.com/docs/vsimaging-dotnet-web/Programming-Imaging_Web-Tutorials-Add_DICOM_Viewer_To_React_And_Asp_Net_Core_Application.html 109. dicom-viewer-react - Codesandbox, https://codesandbox.io/s/dicom-viewer-react-vxz8u9 110. Introduction | MedDream, https://meddream.com/documentation/installation-integration-guide/introduction/ 111. Artificial Intelligence (AI), General Data Protection Regulation (GDPR) and Cybersecurity: 10 Misconceptions About Medical Device Software - Veranex, https://veranex.com/2025/03/25/artificial-intelligence-ai-general-data-protection-regulation-gdpr-and-cybersecurity-10-misconceptions-about-medical-device-software/ 112. The Intersection of GDPR and AI and 6 Compliance Best Practices | Exabeam, https://www.exabeam.com/explainers/gdpr-compliance/the-intersection-of-gdpr-and-ai-and-6-compliance-best-practices/ 113. Artificial Intelligence, Medical Devices And Gdpr In Healthcare: Everything You Need To Know About The Current Legal Frame - Global Law Experts, https://globallawexperts.com/artificial-intelligence-medical-devices-and-gdpr-in-healthcare-everything-you-need-to-know-about-the-current-legal-frame/ 114. 9 Best HIPAA Compliance Tools in 2025 | Scytale, https://scytale.ai/resources/best-hipaa-compliance-tools/ 115. How to Build HIPAA-Compliant AI Applications for Healthcare - MobiDev, https://mobidev.biz/blog/how-to-build-hipaa-compliant-ai-applications 116. Building HIPAA-Compliant Software: Best Practices for Healthcare Providers in 2025, https://mobidev.biz/blog/hipaa-compliant-software-development-checklist 117. How to Ensure AI Compliance with GDPR Regulations | Advisera, https://advisera.com/articles/ai-from-gdpr-perspective/ 118. AI in Healthcare: Key HIPAA Compliance Requirements - Momentum, https://www.themomentum.ai/blog/ai-and-hipaa-compliance-in-healthcare-all-you-need-to-know 119. HIPAA and AI: Navigating Compliance in the Age of Artificial Intelligence, https://www.hipaavault.com/resources/hipaa-and-ai-navigating-compliance-in-the-age-of-artificial-intelligence/ 120. GDPR Software Requirements: A Complete Guide - CookieYes, https://www.cookieyes.com/blog/gdpr-software-requirements/ 121. What does the EU GDPR mean for your medical device product? - Extra Horizon, https://www.extrahorizon.com/gdpr-software-medtech-medical-device-eu-regulation-mdr-data-privacy 122. HIPAA Compliance in AI-Powered Medical Scribing - Chase Clinical Documentation, https://www.chaseclinicaldocumentation.com/hipaa-compliance-in-ai-powered-medical-scribing 123. Navigating AI in Healthcare: The EDPB's Guidance on GDPR Compliance | Freyr - Global Regulatory Solutions and Services Company, https://www.freyrsolutions.com/blog/navigating-ai-in-healthcare-the-edpbs-guidance-on-gdpr-compliance 124. 10 Essential HIPAA-Compliant Software Requirements - Blaze.tech, https://www.blaze.tech/post/hipaa-compliant-software-requirements 125. Publicly Available Dental Image Datasets for Artificial Intelligence - PMC - PubMed Central, https://pmc.ncbi.nlm.nih.gov/articles/PMC11633071/ 126. Publicly Available Dental Image Datasets for Artificial Intelligence - Semantic Scholar, https://pdfs.semanticscholar.org/5962/3e84250f373554d9def8d77cbe2e887f2569.pdf 127. (PDF) Clinical Annotation and Segmentation Tool (CAST) Implementation for Dental Diagnostics - ResearchGate, https://www.researchgate.net/publication/375616303_Clinical_Annotation_and_Segmentation_Tool_CAST_Implementation_for_Dental_Diagnostics 128. Combining public datasets for automated tooth assessment in panoramic radiographs - PMC - PubMed Central, https://pmc.ncbi.nlm.nih.gov/articles/PMC10964594/ 129. Data Annotation Tool for Medical | Keylabs, https://keylabs.ai/medical.html 130. Deep Learning Models for Classification of Dental Diseases Using Orthopantomography X-ray OPG Images - MDPI, https://www.mdpi.com/1424-8220/22/19/7370 131. The Best Tech Stacks for AI-Powered Applications in 2025 - DEV Community, https://dev.to/elliot_brenya/the-best-tech-stacks-for-ai-powered-applications-in-2025-efe 132. AI Tech Stack: Choose The Right Technology for Your Software Development Project, https://www.tristatetechnology.com/blog/ultimate-ai-tech-stack-guide 133. AI Tech Stack: Choosing the Right Technology for Your Software - Appinventiv, https://appinventiv.com/blog/choosing-the-right-ai-tech-stack/ 134. How to Select the Right AI Tech Stack for Web Development - GraffersID, https://graffersid.com/how-to-select-the-right-ai-tech-stack-for-web-development/ 135. Node.js vs Python: Which Backend Technology to Choose in 2025? - Mobilunity, https://mobilunity.com/blog/node-js-vs-python/ 136. Node.js vs Python: Comparison & Applications in Real Life - NxtWave, https://www.ccbp.in/blog/articles/node-js-vs-python 137. django, flask or Node? - Reddit, https://www.reddit.com/r/django/comments/10kqmpj/django_flask_or_node/ 138. AI Agent Frameworks Benchmarks Types Examples and Marketplace Review A Comprehensive List, http://www.deepnlp.org/blog/ai-agent-review-benchmarks-and-environment-a-comprehensive-list 139. AI Agent Frameworks: Choosing the Right Foundation for Your Business | IBM, https://www.ibm.com/think/insights/top-ai-agent-frameworks 140. LangChain vs. AutoGen: Which AI Agent Framework is Better? - TextCortex, https://textcortex.com/post/langchain-vs-autogen 141. AutoGen vs LangChain: Comparison for LLM Applications - PromptLayer, https://blog.promptlayer.com/autogen-vs-langchain/ 142. Top 7 Frameworks for Building AI Agents in 2025 - Analytics Vidhya, https://www.analyticsvidhya.com/blog/2024/07/ai-agent-frameworks/ 143. LangChain vs LlamaIndex: In-Depth Comparison and Use - Deepchecks, https://www.deepchecks.com/langchain-vs-llamaindex-depth-comparison-use/ 144. Langchain vs LlamaIndex - A Detailed Comparison - ProjectPro, https://www.projectpro.io/article/langchain-vs-llamaindex/1036 145. LlamaIndex vs. LangChain: Which RAG Tool is Right for You? - n8n Blog, https://blog.n8n.io/llamaindex-vs-langchain/ 146. Neo4j LLM Knowledge Graph Builder - Extract Nodes and Relationships from Unstructured Text, https://neo4j.com/labs/genai-ecosystem/llm-graph-builder/ 147. Provisioning API Keys | Programmatic Control of OpenRouter API Keys, https://openrouter.ai/docs/features/provisioning-api-keys 148. Building Knowledge Graphs[Book] - O'Reilly, https://www.oreilly.com/library/view/building-knowledge-graphs/9781098127091/ 149. Buyer's Guide For Graph Databases - TigerGraph, https://www.tigergraph.com.cn/wp-content/uploads/2021/06/TigerGraph-Buyers-Guide-Comparison.pdf 150. Compare Neo4j Graph Database vs. Tigergraph - G2, https://www.g2.com/compare/neo4j-graph-database-vs-tigergraph 151. Best Graph Database for Enterprise: Neo4j vs TigerGraph vs Dgraph vs NebulaGraph Comparison, https://www.nebula-graph.io/posts/best-graph-database-for-enterprise 152. Graph Database Battle: Neo4j, TigerGraph, and ArangoDB Compared - RisingWave, https://risingwave.com/blog/graph-database-battle-neo4j-tigergraph-and-arangodb-compared/ 153. Weaviate: Pinecone Vs Milvus Comparison - Restack, https://www.restack.io/p/weaviate-answer-pinecone-vs-milvus-cat-ai 154. milvus.io, https://milvus.io/ai-quick-reference/what-vector-databases-are-best-for-semantic-search-applications#:~:text=Pinecone%20simplifies%20deployment%20but%20locks,but%20has%20a%20smaller%20community. 155. Healthcare App Design Guide: How to Improve Patient Medical Experience - Topflight Apps, https://topflightapps.com/ideas/healthcare-mobile-app-design/ 156. Flutter vs React Native – Which is Better for Your Project? | Blog - Droids On Roids, https://www.thedroidsonroids.com/blog/flutter-vs-react-native-comparison 157. Flutter vs. React Native: Which Framework to Choose for Your Next Mobile App Development? - Netguru, https://www.netguru.com/blog/flutter-vs-react-native 158. Flutter vs React Native: Full Comparison Guide 2025 - Langate, https://langate.com/news-and-blog/flutter-vs-react-native-comparison/ 159. React Native vs. Flutter: What Is the Best Framework for Cross-Platform App Development?, https://www.themomentum.ai/blog/react-native-vs-flutter-choosing-the-right-framework-for-cross-platform-app-development 160. pydicom/pydicom: Read, modify and write DICOM files with python code - GitHub, https://github.com/pydicom/pydicom 161. pydicom.dataset.Dataset — pydicom 3.1.0.dev0 documentation, https://pydicom.github.io/pydicom/dev/reference/generated/pydicom.dataset.Dataset.html 162. read and open dicom images using python - Stack Overflow, https://stackoverflow.com/questions/48185544/read-and-open-dicom-images-using-python 163. General examples — pydicom 3.0.1 documentation, https://pydicom.github.io/pydicom/stable/auto_examples/index.html 164. python 3.x - Medical image processing using DICOM images - Stack Overflow, https://stackoverflow.com/questions/57106184/medical-image-processing-using-dicom-images 165. AWS vs GCP vs Azure: Which Cloud Platform is Best for your Business - Qovery, https://www.qovery.com/blog/aws-vs-gcp-vs-azure/ 166. AWS vs Azure vs GCP Comparison : Best Cloud Platform Guide - Veritis, https://www.veritis.com/blog/aws-vs-azure-vs-gcp-the-cloud-platform-of-your-choice/ 167. AWS vs. Azure vs. Google Cloud: A Complete Comparison - DataCamp, https://www.datacamp.com/blog/aws-vs-azure-vs-gcp 168. Choosing a Cloud Service Provider: AWS vs Azure vs GCP - AppIt Ventures, https://appitventures.com/blog/choosing-cloud-service-provider-amazon-web-services-aws-vs-google-cloud-platform-gcp 169. [2412.05536] Comprehensive Evaluation of Multimodal AI Models in Medical Imaging Diagnosis: From Data Augmentation to Preference-Based Comparison - arXiv, https://arxiv.org/abs/2412.05536 170. Comprehensive Evaluation of Multimodal AI Models in Medical Imaging Diagnosis: From Data Augmentation to Preference-Based Comparison - arXiv, https://arxiv.org/html/2412.05536v1 171. Artificial intelligence in dentistry—A review - PMC, https://pmc.ncbi.nlm.nih.gov/articles/PMC11811754/ 172. Machine Learning in Dentistry: A Scoping Review - PMC - PubMed Central, https://pmc.ncbi.nlm.nih.gov/articles/PMC9918184/ 173. Multimodal AI and Large Language Models for Orthopantomography Radiology Report Generation and Q&A - MDPI, https://www.mdpi.com/2571-5577/8/2/39 174. From Text to Multimodality: Exploring the Evolution and Impact of Large Language Models in Medical Practice - OpenReview, https://openreview.net/pdf?id=xG3JP8z6iE 175. Machine learning in dental, oral and craniofacial imaging: a review of recent progress - PMC, https://pmc.ncbi.nlm.nih.gov/articles/PMC8136280/ 176. Dental Imaging Analytics Platforms - ESP on CB Insights, https://www.cbinsights.com/esp/healthcare-&-life-sciences/monitoring,-imaging-&-diagnostics-tech/dental-imaging-analytics-platforms 177. The Inexorability of Artificial Intelligence: Where, Why, and How AI Is Being Embraced By the Dental Industry - Dental Products Report, https://www.dentalproductsreport.com/view/the-inexorability-of-artificial-intelligence-where-why-and-how-ai-is-being-embraced-by-the-dental-industry 178. Exploring the Applications of Artificial Intelligence in Dental Image Detection: A Systematic Review - PMC, https://pmc.ncbi.nlm.nih.gov/articles/PMC11545562/ 179. Dental AI's big 2024: 30 updates to know, https://www.beckersdental.com/ai-teledentistry/dental-ais-big-2024-30-updates-to-know/ 180. Transforming dental diagnostics with artificial intelligence: advanced integration of ChatGPT and large language models for patient care - PubMed Central, https://pmc.ncbi.nlm.nih.gov/articles/PMC11797834/ 181. Insights into Predicting Tooth Extraction from Panoramic Dental Images: Artificial Intelligence vs. Dentists - PMC, https://pmc.ncbi.nlm.nih.gov/articles/PMC11182848/ 182. VideaHealth: The Dental AI Trusted by Dentists and DSOs, https://www.videa.ai/ 183. Dentistry Dashboard vs. Overjet, Pearl, CareStack & Other AI Dental Platforms, https://dentistrydashboard.com/dentistry-dashboard-vs-overjet-pearl-carestack-other-ai-dental-platforms 184. 9 Best AI Dental Software Solutions You Need to Know About - Teero, https://www.teero.com/blog/ai-dental-software 185. Overjet Dental AI vs Pearl Dental AI Software Comparison, https://www.overjet.com/overjet-vs-pearl-dental-ai 186. Multimodal Reasoning with Multimodal Knowledge Graph - arXiv, https://arxiv.org/html/2406.02030v2 187. Multi-Modal Knowledge Graph Construction and Application: A Survey - arXiv, https://arxiv.org/pdf/2202.05786 188. Rule-based Text Extraction for Multimodal Knowledge Graph - The Science and Information (SAI) Organization, https://thesai.org/Downloads/Volume13No5/Paper_35-Rule_Based_Text_Extraction_for_Multimodal_Knowledge.pdf 189. Multimodal RAG: Boosting Search Precision & Relevance | GigaSpaces AI, https://www.gigaspaces.com/blog/multimodal-rag-boosting-search-precision-relevance 190. A systematic literature review of knowledge graph construction and application in education, https://pmc.ncbi.nlm.nih.gov/articles/PMC10847940/ 191. T2KG: Transforming Multimodal Document to Knowledge Graph - ACL Anthology, https://aclanthology.org/2023.ranlp-1.43.pdf 192. A Survey on Multimodal Knowledge Graphs: Construction, Completion and Applications, https://www.mdpi.com/2227-7390/11/8/1815 193. RULE: Reliable Multimodal RAG for Factuality in Medical Vision Language Models - arXiv, https://arxiv.org/html/2407.05131v1 194. Explainable Artificial Intelligence in Medical Decision Support Systems - IET Digital Library, https://digital-library.theiet.org/doi/book/10.1049/pbhe050e 195. XAI-Based Clinical Decision Support Systems: A Systematic Review - MDPI, https://www.mdpi.com/2076-3417/14/15/6638 196. How to Visualize Machine Learning Models - SHAP and LIME - Atlantic.Net, https://www.atlantic.net/gpu-server-hosting/how-to-visualize-machine-learning-models-with-shap-and-lime/ 197. Explainable AI, LIME & SHAP for Model Interpretability | Unlocking AI's Decision-Making, https://www.datacamp.com/tutorial/explainable-ai-understanding-and-trusting-machine-learning-models 198. Practical guide to SHAP analysis: Explaining supervised machine learning model predictions in drug development - PMC, https://pmc.ncbi.nlm.nih.gov/articles/PMC11513550/ 199. An introduction to explainable artificial intelligence with LIME and SHAP - UB, https://diposit.ub.edu/dspace/bitstream/2445/192075/1/tfg_nieto_juscafresa_aleix.pdf 200. Spectral Zones-Based SHAP/LIME: Enhancing Interpretability in Spectral Deep Learning Models Through Grouped Feature Analysis | Analytical Chemistry - ACS Publications, https://pubs.acs.org/doi/10.1021/acs.analchem.4c02329 201. Example of an explanation interface visualizing a User style... - ResearchGate, https://www.researchgate.net/figure/Example-of-an-explanation-interface-visualizing-a-User-style-explanation-using-the_fig6_345599246 202. Machine Learning–Based Interpretation and Visualization of Nonlinear Interactions in Prostate Cancer Survival - ASCO Publications, https://ascopubs.org/doi/10.1200/CCI.20.00002 203. Overview of basic design recommendations for user-centered explanation interfaces for AI-based clinical decision support systems: A scoping review - PubMed, https://pubmed.ncbi.nlm.nih.gov/39866885/ 204. Understanding XAI: SHAP, LIME, And Other Key Techniques, https://aicompetence.org/understanding-xai-shap-lime-and-beyond/ 205. A Pipeline for the Implementation and Visualization of Explainable Machine Learning for Medical Imaging Using Radiomics Features - MDPI, https://www.mdpi.com/1424-8220/22/14/5205 206. A Comparative Analysis of LIME and SHAP Interpreters With Explainable ML-Based Diabetes Predictions - DiVA portal, https://www.diva-portal.org/smash/get/diva2:1886442/FULLTEXT02.pdf 207. HL7 FHIR Integrations in Python - Zato, https://zato.io/en/blog/hl7-fhir-api-integrations-python.html 208. Creating and managing FHIR resources | Cloud Healthcare API, https://cloud.google.com/healthcare-api/docs/how-tos/fhir-resources 209. Open Source Implementations - FHIR - HL7 Confluence, https://confluence.hl7.org/display/FHIR/Open+Source+Implementations 210. FHIR library guidance - Developer and integration hub, https://developer.nhs.uk/apis/gpconnect-1-1-0/development_fhir_open_source_guidance.html 211. Managing FHIR resources using FHIR bundles | Cloud Healthcare API - Google Cloud, https://cloud.google.com/healthcare-api/docs/how-tos/fhir-bundles 212. CDSS - Clinical Decision Support System - Digital Scientists, https://digitalscientists.com/healthcare/solutions/cdss-clinical-decision-support-system/ 213. fhir.resources - PyPI, https://pypi.org/project/fhir.resources/ 214. What is a Clinical Decision Support System? - Jelvix, https://jelvix.com/blog/clinical-decision-support 215. Clinical Decision Support System Development Services - Mindbowser, https://www.mindbowser.com/clinical-decision-support-systems/ 216. Custom Clinical Decision Support System: A Full Guide - ScienceSoft, https://www.scnsoft.com/healthcare/clinical-decision-support-system 217. FDA Needs a New Approach to AI/ML-Enabled Medical Devices | Mintz, https://www.mintz.com/insights-center/viewpoints/2146/2024-03-12-fda-needs-new-approach-aiml-enabled-medical-devices 218. NHC Submits Comments on FDA Draft Guidance for AI/ML-Enabled Medical Devices, https://nationalhealthcouncil.org/letters-comments/nhc-submits-comments-on-fda-draft-guidance-for-ai-ml-enabled-medical-devices/ 219. Considerations for the Use of Artificial Intelligence To Support Regulatory Decision-Making for Drug and Biological Products | FDA, https://www.fda.gov/regulatory-information/search-fda-guidance-documents/considerations-use-artificial-intelligence-support-regulatory-decision-making-drug-and-biological 220. Introduction to EU Artificial Intelligence Act - Veranex, https://veranex.com/wp-content/uploads/2024/03/White-Paper_EU-AI-in-healthcare.pdf 221. CORE-MD clinical risk score for regulatory evaluation of artificial intelligence-based medical device software, https://pmc.ncbi.nlm.nih.gov/articles/PMC11802784/ 222. Will the EU Medical Device Regulation help to improve the safety and performance of medical AI devices? - PMC, https://pmc.ncbi.nlm.nih.gov/articles/**********/ 223. CDSCO Guidance on Good Clinical Practices: Ethical and Safety Considerations - RegDesk, https://www.regdesk.co/cdsco-guidance-on-good-clinical-practices-ethical-and-safety-considerations/ 224. CDSCO Guidance on Good Clinical Practices: Monitoring | India - RegDesk, https://www.regdesk.co/cdsco-guidance-on-good-clinical-practices-monitoring/ 225. Essential Principles for Safety Performance of Medical Devices - cdsco, https://cdsco.gov.in/opencms/export/sites/CDSCO_WEB/Pdf-documents/medical-device/Essentialprinciples.pdf 226. Updates on Medical Device and IVD Regulation in India - PMDA, https://www.pmda.go.jp/files/000250286.pdf 227. Digital Health Regulation In India - APACMed, https://apacmed.org/wp-content/uploads/2021/10/Digital-Health-Regulation-in-India_Position-Paper.pdf 228. Guidelines_Grouping_of_MDand, https://cdsco.gov.in/opencms/export/sites/CDSCO_WEB/Pdf-documents/medical-device/Guidelines_Grouping_of_MDandIVD.pdf 229. Healthcare in the Digital Age: India's Ayushman Bharat Digital Mission, https://intpolicydigest.org/the-platform/healthcare-in-the-digital-age-india-s-ayushman-bharat-digital-mission/ 230. Ayushman Bharat Digital Mission marks a Transformative Three-Year Journey towards enabling Digital Health, https://www.mohfw.gov.in/?q=pressrelease-87 231. Full article: The Ayushman Bharat Digital Mission of India: An Assessment, https://www.tandfonline.com/doi/full/10.1080/23288604.2024.2392290 232. A Glimpse Into the Deployment of Digital Health in India - Telehealth and Medicine Today, https://telehealthandmedicinetoday.com/index.php/journal/article/view/450/1042 233. Understanding the Digital Personal Data Protection Act, 2023: What It Means for Healthcare Providers - Eka Care, https://www.eka.care/services/understanding-the-digital-personal-data-protection-act-2023-what-it-means-for-healthcare-providers 234. Do We Need a Separate Health Data Law in India? - Center for Internet and Society, https://cis-india.org/internet-governance/blog/do-we-need-separate-health-data-law-in-india