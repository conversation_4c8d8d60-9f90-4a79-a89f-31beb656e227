#!/usr/bin/env python3
"""
Script to extract content from the Dental AI Chat with Images.docx file
"""

import os
from docx import Document

def extract_dental_ai_content():
    """Extract content from the Dental AI document"""
    
    docx_path = "Dental AI Chat with Images.docx"
    
    if not os.path.exists(docx_path):
        print(f"❌ File not found: {docx_path}")
        return None
    
    try:
        doc = Document(docx_path)
        content = []
        
        print("📄 Extracting content from Dental AI Chat with Images.docx...")
        print("=" * 60)
        
        # Extract paragraphs
        for i, para in enumerate(doc.paragraphs):
            if para.text.strip():
                content.append(f"Para {i+1}: {para.text}")
                print(f"Para {i+1}: {para.text}")
        
        # Extract tables
        for i, table in enumerate(doc.tables):
            print(f"\n--- TABLE {i+1} ---")
            content.append(f"\n--- TABLE {i+1} ---")
            
            for row_idx, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_data.append(cell.text.strip())
                
                if row_data:
                    row_text = " | ".join(row_data)
                    print(f"Row {row_idx+1}: {row_text}")
                    content.append(f"Row {row_idx+1}: {row_text}")
            
            print("--- END TABLE ---\n")
            content.append("--- END TABLE ---\n")
        
        # Save extracted content
        with open("dental_ai_extracted_content.txt", "w", encoding="utf-8") as f:
            f.write("\n".join(content))
        
        print(f"\n✅ Content extracted and saved to dental_ai_extracted_content.txt")
        return content
        
    except Exception as e:
        print(f"❌ Error extracting content: {e}")
        return None

if __name__ == "__main__":
    extract_dental_ai_content()
